(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[871],{5196:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},6101:(e,r,t)=>{"use strict";t.d(r,{s:()=>o,t:()=>n});var s=t(12115);function a(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function n(...e){return r=>{let t=!1,s=e.map(e=>{let s=a(e,r);return t||"function"!=typeof s||(t=!0),s});if(t)return()=>{for(let r=0;r<s.length;r++){let t=s[r];"function"==typeof t?t():a(e[r],null)}}}}function o(...e){return s.useCallback(n(...e),e)}},16785:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},25552:(e,r,t)=>{"use strict";t.d(r,{G6:()=>d,Ru:()=>l,bk:()=>o,signOutUser:()=>i});var s=t(16203),a=t(98915);let n=new s.HF;n.setCustomParameters({prompt:"select_account"});let o=async(e,r,t)=>{try{let n=await (0,s.eJ)(a.j2,e,r);return t&&n.user&&await (0,s.r7)(n.user,{displayName:t}),n.user}catch(e){throw console.error("Sign up error:",e),Error(c(e.code))}},l=async(e,r)=>{try{return(await (0,s.x9)(a.j2,e,r)).user}catch(e){throw console.error("Sign in error:",e),Error(c(e.code))}},d=async()=>{try{return(await (0,s.df)(a.j2,n)).user}catch(e){if(console.error("Google sign in error:",e),"auth/popup-closed-by-user"===e.code||"auth/cancelled-popup-request"===e.code)throw e;throw Error(c(e.code))}},i=async()=>{try{await (0,s.CI)(a.j2)}catch(e){throw console.error("Sign out error:",e),Error("Failed to sign out")}},c=e=>{switch(e){case"auth/user-not-found":return"No account found with this email address. Please check your email or sign up for a new account.";case"auth/wrong-password":return"Incorrect password. Please try again or reset your password.";case"auth/invalid-credential":return"Invalid email or password. Please check your credentials and try again.";case"auth/email-already-in-use":return"An account with this email already exists. Please sign in instead.";case"auth/weak-password":return"Password should be at least 6 characters long.";case"auth/invalid-email":return"Please enter a valid email address.";case"auth/user-disabled":return"This account has been disabled. Please contact support.";case"auth/too-many-requests":return"Too many failed attempts. Please try again later or reset your password.";case"auth/popup-closed-by-user":return"Sign-in popup was closed. Please try again.";case"auth/popup-blocked":return"Sign-in popup was blocked. Please allow popups and try again.";case"auth/network-request-failed":return"Network error. Please check your internet connection and try again.";case"auth/cancelled-popup-request":return"Sign-in was cancelled.";case"auth/account-exists-with-different-credential":return"An account already exists with the same email address but different sign-in credentials.";case"auth/operation-not-allowed":return"This sign-in method is not enabled. Please contact support.";case"auth/invalid-api-key":return"Invalid API key. Please check your Firebase configuration.";case"auth/app-deleted":return"Firebase app has been deleted. Please check your configuration.";case"auth/invalid-user-token":return"User token is invalid. Please sign in again.";case"auth/user-token-expired":return"User token has expired. Please sign in again.";default:return"Authentication error: ".concat(e||"Unknown error")}}},35695:(e,r,t)=>{"use strict";var s=t(18999);t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},38564:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},43876:(e,r,t)=>{Promise.resolve().then(t.bind(t,86621))},47863:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},47924:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},50395:(e,r,t)=>{"use strict";t.d(r,{A:()=>c,O:()=>i});var s=t(95155),a=t(12115),n=t(16203),o=t(98915),l=t(25552);let d=(0,a.createContext)(void 0);function i(e){let{children:r}=e,[t,i]=(0,a.useState)(null),[c,m]=(0,a.useState)(!0);(0,a.useEffect)(()=>{let e=(0,n.hg)(o.j2,e=>{i(e),m(!1)});return()=>e()},[]);let u=async(e,r)=>{try{return await (0,l.Ru)(e,r)}catch(e){throw console.error("Sign in error:",e),e}},x=async()=>{try{await (0,l.signOutUser)()}catch(e){throw console.error("Sign out error:",e),e}},h=async()=>{try{return await (0,l.G6)()}catch(e){throw console.error("Google sign in error:",e),e}};return(0,s.jsx)(d.Provider,{value:{user:t,loading:c,signIn:u,signOut:x,signInWithGoogle:h},children:r})}function c(){let e=(0,a.useContext)(d);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},53999:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var s=t(52596),a=t(39688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,s.$)(r))}},66474:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},74466:(e,r,t)=>{"use strict";t.d(r,{F:()=>o});var s=t(52596);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=s.$,o=(e,r)=>t=>{var s;if((null==r?void 0:r.variants)==null)return n(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:o,defaultVariants:l}=r,d=Object.keys(o).map(e=>{let r=null==t?void 0:t[e],s=null==l?void 0:l[e];if(null===r)return null;let n=a(r)||a(s);return o[e][n]}),i=t&&Object.entries(t).reduce((e,r)=>{let[t,s]=r;return void 0===s||(e[t]=s),e},{});return n(e,d,null==r?void 0:null===(s=r.compoundVariants)||void 0===s?void 0:s.reduce((e,r)=>{let{class:t,className:s,...a}=r;return Object.entries(a).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...l,...i}[r]):({...l,...i})[r]===t})?[...e,t,s]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},75525:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},76517:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("Wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},86621:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>q});var s=t(95155),a=t(12115),n=t(97636);let o=[{symbol:"BTCUSDT",baseAsset:"BTC",displayName:"Bitcoin",category:"major"},{symbol:"ETHUSDT",baseAsset:"ETH",displayName:"Ethereum",category:"major"},{symbol:"BNBUSDT",baseAsset:"BNB",displayName:"BNB",category:"major"},{symbol:"XRPUSDT",baseAsset:"XRP",displayName:"XRP",category:"altcoin"},{symbol:"SOLUSDT",baseAsset:"SOL",displayName:"Solana",category:"altcoin"},{symbol:"ADAUSDT",baseAsset:"ADA",displayName:"Cardano",category:"altcoin"},{symbol:"TRXUSDT",baseAsset:"TRX",displayName:"Tron",category:"altcoin"},{symbol:"SUIUSDT",baseAsset:"SUI",displayName:"Sui",category:"altcoin"},{symbol:"LINKUSDT",baseAsset:"LINK",displayName:"Chainlink",category:"altcoin"},{symbol:"AVAXUSDT",baseAsset:"AVAX",displayName:"Avalanche",category:"altcoin"},{symbol:"XLMUSDT",baseAsset:"XLM",displayName:"Stellar",category:"altcoin"},{symbol:"HBARUSDT",baseAsset:"HBAR",displayName:"Hedera",category:"altcoin"},{symbol:"TONUSDT",baseAsset:"TON",displayName:"Toncoin",category:"altcoin"},{symbol:"DOTUSDT",baseAsset:"DOT",displayName:"Polkadot",category:"altcoin"},{symbol:"LTCUSDT",baseAsset:"LTC",displayName:"Litecoin",category:"altcoin"},{symbol:"XMRUSDT",baseAsset:"XMR",displayName:"Monero",category:"altcoin"},{symbol:"NEARUSDT",baseAsset:"NEAR",displayName:"Near Protocol",category:"altcoin"},{symbol:"APTUSDT",baseAsset:"APT",displayName:"Aptos",category:"altcoin"},{symbol:"ICPUSDT",baseAsset:"ICP",displayName:"Internet Computer",category:"altcoin"},{symbol:"ARUSDT",baseAsset:"AR",displayName:"Arweave",category:"altcoin"},{symbol:"DOGEUSDT",baseAsset:"DOGE",displayName:"Dogecoin",category:"meme"},{symbol:"SHIBUSDT",baseAsset:"SHIB",displayName:"Shiba Inu",category:"meme"},{symbol:"PEPEUSDT",baseAsset:"PEPE",displayName:"Pepe",category:"meme"},{symbol:"UNIUSDT",baseAsset:"UNI",displayName:"Uniswap",category:"defi"},{symbol:"ONDOUSDT",baseAsset:"ONDO",displayName:"Ondo",category:"defi"}],l=e=>{let r=o.find(r=>r.symbol===e);return(null==r?void 0:r.displayName)||e.replace("USDT","")},d=()=>o.map(e=>e.symbol),i=e=>{let r=e.replace("USDT","");return"BINANCE:".concat(r,"USDT.P")};function c(e){let{symbol:r="BTCUSDT",interval:t="15",height:o="100%",containerClassName:l=""}=e,{resolvedTheme:d}=(0,n.D)(),c=(0,a.useRef)(null),m=(0,a.useRef)(!1),u=i(r);return(0,a.useEffect)(()=>{let e=()=>{if(!c.current)return;for(;c.current.firstChild;)c.current.removeChild(c.current.firstChild);let e=document.createElement("div");e.className="tradingview-widget-container w-full h-full",e.style.height="100%",e.style.width="100%";let r=document.createElement("div");r.id="tradingview_widget",r.style.height="100%",r.style.width="100%",e.appendChild(r),c.current.appendChild(e);try{new window.TradingView.widget({container_id:"tradingview_widget",autosize:!0,symbol:u,interval:t,timezone:"Etc/UTC",theme:d,style:"1",locale:"en",toolbar_bg:"rgba(0, 0, 0, 0)",enable_publishing:!1,allow_symbol_change:!1,hide_top_toolbar:!1,hide_legend:!1,save_image:!0,hide_volume:!1,backgroundColor:"dark"===d?"#131722":"#ffffff",gridColor:"rgba(0, 0, 0, 0)",hide_side_toolbar:!1,studies:[],fullscreen:!1,width:"100%",height:"100%",disabled_features:["header_symbol_search","header_compare","header_undo_redo","header_screenshot","grid_lines","horz_grid_lines","vert_grid_lines"],enabled_features:["use_localstorage_for_settings","side_toolbar_in_fullscreen_mode"],overrides:{"paneProperties.background":"dark"===d?"#131722":"#ffffff","paneProperties.vertGridProperties.color":"rgba(0, 0, 0, 0)","paneProperties.horzGridProperties.color":"rgba(0, 0, 0, 0)","scalesProperties.lineColor":"dark"===d?"#363c4e":"#dde1e5"}}),m.current=!0}catch(r){console.error("Error creating TradingView widget:",r);let e=document.createElement("div");e.className="flex items-center justify-center h-full",e.innerHTML='\n          <div class="text-center p-4">\n            <p class="text-lg font-medium mb-2">Chart could not be loaded</p>\n            <p class="text-sm text-muted-foreground">Please check your internet connection and try again</p>\n          </div>\n        ',c.current&&(c.current.innerHTML="",c.current.appendChild(e))}};if(window.TradingView)e();else{let r=document.createElement("script");r.src="https://s3.tradingview.com/tv.js",r.async=!0,r.onload=e,r.onerror=()=>{console.error("Failed to load TradingView script");let e=document.createElement("div");e.className="flex items-center justify-center h-full",e.innerHTML='\n          <div class="text-center p-4">\n            <p class="text-lg font-medium mb-2">Chart could not be loaded</p>\n            <p class="text-sm text-muted-foreground">Please check your internet connection and try again</p>\n          </div>\n        ',c.current&&(c.current.innerHTML="",c.current.appendChild(e))},document.head.appendChild(r)}return()=>{c.current&&(c.current.innerHTML="")}},[u,t,d]),(0,s.jsx)("div",{ref:c,className:"tradingview-widget-container w-full h-full rounded-lg border border-border overflow-hidden ".concat(l),style:{height:o}})}var m=t(47863),u=t(66474),x=t(75525),h=t(34135),p=t(56671);function f(e){let{symbol:r,lastPrice:t,leverage:n,onOrderSubmit:o}=e,{placeOrder:l,isLoading:d,accountInfo:i,getAvailableBalance:c}=(0,h.fx)(),[f,b]=(0,a.useState)("MARKET"),[g,y]=(0,a.useState)("BUY"),[N,v]=(0,a.useState)(""),[j,w]=(0,a.useState)(t),[k,S]=(0,a.useState)([25,50,75,100]),[P,T]=(0,a.useState)(""),[C,A]=(0,a.useState)(""),[E,U]=(0,a.useState)(0),[F,L]=(0,a.useState)(0),[R,D]=(0,a.useState)(!1),O=n||10,I=(null==i?void 0:i.totalWalletBalance)||1e4,M=c();if(null==M||0===M){let e=(null==i?void 0:i.totalUnrealizedProfit)||0;M=Math.max(0,I-((null==i?void 0:i.totalPositionInitialMargin)||0)+e)}(0,a.useEffect)(()=>{"MARKET"===f&&w(t)},[t,f]);let B=()=>!t||M<=0?"0":(M*O/Number.parseFloat(t)).toFixed(4),_=e=>{v((Number.parseFloat(B())*e/100).toFixed(4))},z=(e,r)=>{if(!j)return;let t=Number.parseFloat(j);"sl"===r?(U(e),"BUY"===g?T((t*(1-e/100)).toFixed(2)):T((t*(1+e/100)).toFixed(2))):(L(e),"BUY"===g?A((t*(1+e/100)).toFixed(2)):A((t*(1-e/100)).toFixed(2)))},W=async e=>{if(e.preventDefault(),!N||0>=Number.parseFloat(N)){p.oR.error("Please enter a valid quantity");return}if("LIMIT"===f&&(!j||0>=Number.parseFloat(j))){p.oR.error("Please enter a valid price");return}let s="MARKET"===f?Number.parseFloat(t):Number.parseFloat(j),a=Number.parseFloat(N)*s/O;if(a>M){p.oR.error("Insufficient balance. Required: ".concat(a.toFixed(2)," USDT, Available: ").concat(M.toFixed(2)," USDT"));return}let n={symbol:r,side:g,type:f,quantity:Number.parseFloat(N),leverage:O};"LIMIT"===f&&(n.price=Number.parseFloat(j)),P&&(n.stopLoss=Number.parseFloat(P)),C&&(n.takeProfit=Number.parseFloat(C));try{console.log("Submitting order from form:",n);let e=await l(n);console.log("Order submitted successfully with ID:",e),o&&o(n),v(""),"LIMIT"===f&&w(t),T(""),A(""),U(0),L(0),p.oR.success("".concat(g," order placed successfully"),{description:"".concat(N," ").concat(r," at ").concat("MARKET"===f?"market price":"$".concat(j)),duration:5e3})}catch(r){console.error("Error submitting order:",r);let e=r instanceof Error?r.message:"Failed to submit order. Please try again.";p.oR.error(e,{duration:8e3,description:"Please check your connection and try again."})}},H=(()=>{if(!N||!j||!P&&!C)return{sl:"0",tp:"0"};let e=Number.parseFloat(N),r=Number.parseFloat(j),t=P?Number.parseFloat(P):0,s=C?Number.parseFloat(C):0,a=0,n=0;return"BUY"===g?(t&&(a=(t-r)*e),s&&(n=(s-r)*e)):(t&&(a=(r-t)*e),s&&(n=(r-s)*e)),{sl:a.toFixed(2),tp:n.toFixed(2)}})(),q="MARKET"===f?Number.parseFloat(t):Number.parseFloat(j),Y=Number.parseFloat(N||"0")*q,V=Y/O,$=V<=M;return(0,s.jsxs)("div",{className:"bg-card rounded-xl border border-border/50 shadow-lg p-4 h-full backdrop-blur-sm",children:[(0,s.jsx)("div",{className:"mb-4",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("h2",{className:"text-lg font-bold text-foreground tracking-tight",children:"Place Order"}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)("div",{className:"font-bold text-emerald-600 text-sm",children:[M.toFixed(2)," USDT"]}),(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:"Available"})]})]})}),(0,s.jsxs)("form",{onSubmit:W,className:"flex flex-col h-[calc(100%-80px)]",children:[(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-xs font-semibold text-foreground mb-1.5",children:"Order Type"}),(0,s.jsxs)("div",{className:"flex rounded-md overflow-hidden border border-border/50 bg-muted/20",children:[(0,s.jsx)("button",{type:"button",className:"flex-1 py-2.5 text-center text-xs font-semibold transition-all duration-200 ".concat("MARKET"===f?"bg-primary text-primary-foreground shadow-sm":"bg-transparent text-muted-foreground hover:bg-muted/50 hover:text-foreground"),onClick:()=>b("MARKET"),children:"Market"}),(0,s.jsx)("button",{type:"button",className:"flex-1 py-2.5 text-center text-xs font-semibold transition-all duration-200 ".concat("LIMIT"===f?"bg-primary text-primary-foreground shadow-sm":"bg-transparent text-muted-foreground hover:bg-muted/50 hover:text-foreground"),onClick:()=>b("LIMIT"),children:"Limit"})]})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-xs font-semibold text-foreground mb-2",children:"Order Side"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,s.jsx)("button",{type:"button",className:"py-2 px-3 rounded-lg font-semibold text-xs transition-all duration-200 ".concat("BUY"===g?"bg-emerald-600 text-white shadow-md shadow-emerald-600/25":"bg-emerald-50 dark:bg-emerald-950/20 border border-emerald-200 dark:border-emerald-800 text-emerald-700 dark:text-emerald-400 hover:bg-emerald-100 dark:hover:bg-emerald-950/40"),onClick:()=>y("BUY"),children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-1",children:[(0,s.jsx)("span",{className:"text-sm",children:"↗"}),(0,s.jsx)("span",{children:"BUY"})]})}),(0,s.jsx)("button",{type:"button",className:"py-2 px-3 rounded-lg font-semibold text-xs transition-all duration-200 ".concat("SELL"===g?"bg-red-600 text-white shadow-md shadow-red-600/25":"bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-950/40"),onClick:()=>y("SELL"),children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-1",children:[(0,s.jsx)("span",{className:"text-sm",children:"↘"}),(0,s.jsx)("span",{children:"SELL"})]})})]})]}),"LIMIT"===f&&(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-xs font-semibold text-foreground mb-1.5",children:"Limit Price"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"number",value:j,onChange:e=>w(e.target.value),className:"w-full p-2.5 text-xs border border-border/50 rounded-md bg-background/50 backdrop-blur-sm pr-14 font-medium focus:border-primary focus:ring-1 focus:ring-primary/20 transition-all duration-200",step:"0.01",min:"0",required:!0,placeholder:"Enter price"}),(0,s.jsxs)("div",{className:"absolute right-1.5 top-1/2 transform -translate-y-1/2 flex border border-border/50 rounded-sm overflow-hidden",children:[(0,s.jsx)("button",{type:"button",className:"bg-muted/50 p-1 hover:bg-muted transition-colors",onClick:()=>w((Number.parseFloat(j)+.01).toString()),children:(0,s.jsx)(m.A,{className:"h-2.5 w-2.5"})}),(0,s.jsx)("button",{type:"button",className:"bg-muted/50 p-1 hover:bg-muted transition-colors border-l border-border/50",onClick:()=>w(Math.max(.01,Number.parseFloat(j)-.01).toString()),children:(0,s.jsx)(u.A,{className:"h-2.5 w-2.5"})})]})]}),(0,s.jsxs)("div",{className:"mt-1.5 text-xs text-muted-foreground",children:["Market: ",(0,s.jsx)("span",{className:"font-medium text-foreground",children:t})]})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-xs font-semibold text-foreground mb-1.5",children:"Quantity"}),(0,s.jsx)("input",{type:"number",value:N,onChange:e=>v(e.target.value),className:"w-full p-2.5 text-xs border border-border/50 rounded-md bg-background/50 backdrop-blur-sm font-medium focus:border-primary focus:ring-1 focus:ring-primary/20 transition-all duration-200",step:"0.0001",min:"0",required:!0,placeholder:"Enter quantity"}),(0,s.jsx)("div",{className:"flex gap-1.5 mt-2",children:k.map(e=>(0,s.jsxs)("button",{type:"button",onClick:()=>_(e),className:"flex-1 text-xs bg-muted/50 border border-border/50 px-2 py-1.5 rounded-md hover:bg-muted hover:border-border font-medium transition-all duration-200",children:[e,"%"]},e))}),(0,s.jsxs)("div",{className:"mt-1.5 text-xs text-muted-foreground",children:["Max: ",(0,s.jsx)("span",{className:"font-medium text-foreground",children:B()})]})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center p-2.5 border border-border/50 rounded-lg cursor-pointer mb-2 hover:border-border transition-all duration-200 bg-muted/20",onClick:()=>D(!R),children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(x.A,{className:"h-3.5 w-3.5 mr-2 text-primary"}),(0,s.jsx)("span",{className:"text-xs font-semibold",children:"SL & TP"})]}),(0,s.jsx)(u.A,{className:"h-3.5 w-3.5 transition-transform duration-200 ".concat(R?"rotate-180":"")})]}),R&&(0,s.jsxs)("div",{className:"p-3 border border-border/30 rounded-lg bg-gradient-to-br from-background/80 to-muted/20 backdrop-blur-sm space-y-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-1.5",children:[(0,s.jsx)("label",{className:"text-xs font-semibold text-foreground",children:"Stop Loss"}),(0,s.jsxs)("span",{className:"text-xs font-bold ".concat(Number.parseFloat(H.sl)>=0?"text-emerald-500":"text-red-500"),children:[H.sl," USDT"]})]}),(0,s.jsx)("input",{type:"number",value:P,onChange:e=>T(e.target.value),className:"w-full p-2 text-xs border border-border/50 rounded-md bg-background/50 backdrop-blur-sm font-medium focus:border-red-400 focus:ring-1 focus:ring-red-400/20 transition-all duration-200 mb-1.5",step:"0.01",min:"0",placeholder:"BUY"===g?"Lower than entry":"Higher than entry"}),(0,s.jsx)("div",{className:"flex gap-1.5",children:[1,2,5,10].map(e=>(0,s.jsxs)("button",{type:"button",className:"flex-1 text-xs px-1.5 py-1 rounded-md font-medium transition-all duration-200 ".concat(E===e?"bg-red-500 text-white shadow-sm":"bg-muted/50 border border-border/50 hover:bg-muted hover:border-border text-muted-foreground hover:text-foreground"),onClick:()=>z(e,"sl"),children:["-",e,"%"]},e))})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-1.5",children:[(0,s.jsx)("label",{className:"text-xs font-semibold text-foreground",children:"Take Profit"}),(0,s.jsxs)("span",{className:"text-xs font-bold ".concat(Number.parseFloat(H.tp)>=0?"text-emerald-500":"text-red-500"),children:[H.tp," USDT"]})]}),(0,s.jsx)("input",{type:"number",value:C,onChange:e=>A(e.target.value),className:"w-full p-2 text-xs border border-border/50 rounded-md bg-background/50 backdrop-blur-sm font-medium focus:border-emerald-400 focus:ring-1 focus:ring-emerald-400/20 transition-all duration-200 mb-1.5",step:"0.01",min:"0",placeholder:"BUY"===g?"Higher than entry":"Lower than entry"}),(0,s.jsx)("div",{className:"flex gap-1.5",children:[1,2,5,10].map(e=>(0,s.jsxs)("button",{type:"button",className:"flex-1 text-xs px-1.5 py-1 rounded-md font-medium transition-all duration-200 ".concat(F===e?"bg-emerald-500 text-white shadow-sm":"bg-muted/50 border border-border/50 hover:bg-muted hover:border-border text-muted-foreground hover:text-foreground"),onClick:()=>z(e,"tp"),children:["+",e,"%"]},e))})]})]})]}),(0,s.jsxs)("div",{className:"bg-gradient-to-r from-muted/60 to-muted/40 p-3 rounded-lg border border-border/30 mb-4",children:[(0,s.jsx)("h3",{className:"text-xs font-bold text-foreground mb-2",children:"Order Summary"}),(0,s.jsxs)("div",{className:"space-y-1.5",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-xs text-muted-foreground",children:"Value:"}),(0,s.jsxs)("span",{className:"font-semibold text-foreground text-sm",children:[Y.toFixed(2)," USDT"]})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-xs text-muted-foreground",children:"Margin:"}),(0,s.jsxs)("span",{className:"font-semibold text-sm ".concat($?"text-foreground":"text-red-500"),children:[V.toFixed(2)," USDT"]})]}),!$&&N&&(0,s.jsxs)("div",{className:"mt-2 p-2 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-md",children:[(0,s.jsx)("div",{className:"text-red-600 dark:text-red-400 text-xs font-semibold",children:"⚠️ Insufficient Balance"}),(0,s.jsxs)("div",{className:"text-red-500 dark:text-red-400 text-xs mt-0.5",children:["Need ",(V-M).toFixed(2)," USDT more"]})]})]})]}),(0,s.jsx)("button",{type:"submit",disabled:d||!$,className:"w-full py-4 rounded-xl font-bold text-base mt-auto transition-all duration-300 transform ".concat("BUY"===g?"bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 shadow-lg shadow-emerald-600/25":"bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 shadow-lg shadow-red-600/25"," text-white ").concat(d||!$?"opacity-70 cursor-not-allowed":"hover:scale-105 hover:shadow-xl active:scale-95"),children:d?(0,s.jsxs)("span",{className:"flex items-center justify-center",children:[(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,s.jsx)("span",{className:"text-lg",children:"Processing Order..."})]}):(0,s.jsxs)("div",{className:"flex items-center justify-center",children:[(0,s.jsx)("span",{className:"text-xl mr-2",children:"BUY"===g?"\uD83D\uDCC8":"\uD83D\uDCC9"}),(0,s.jsx)("span",{className:"text-lg font-bold",children:"".concat("BUY"===g?"BUY / LONG":"SELL / SHORT"," ").concat(r.replace("USDT",""))})]})})]})]})}var b=t(76517),g=t(19946);let y=(0,g.A)("WifiOff",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),N=(0,g.A)("ArrowUpDown",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]]);var v=t(54416),j=t(47924),w=t(38564),k=t(50395),S=t(35695),P=t(50475);function T(){var e;let{user:r,loading:t}=(0,k.A)(),[n,o]=(0,a.useState)(null),[l,d]=(0,a.useState)(!0);return((0,S.useRouter)(),(0,a.useEffect)(()=>P.A.subscribe(e=>{o(e),d(!1)}),[]),t||l)?(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-yellow-500 rounded-full animate-pulse"}),(0,s.jsx)("span",{className:"text-xs text-muted-foreground",children:"Checking..."})]}):r?(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,s.jsxs)("span",{className:"text-xs text-green-600 dark:text-green-400",children:[(null==n?void 0:null===(e=n.balance)||void 0===e?void 0:e.current)||1e4," USDT"]})]}):(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),(0,s.jsx)("span",{className:"text-xs text-red-600 dark:text-red-400",children:"Not Auth"})]})}function C(e){let{symbol:r,onSymbolChange:t,leverage:n=10,onLeverageChange:o,isCrossMargin:i=!0,onCrossMarginChange:c,connectionStatus:x="connected",onRetryConnection:h}=e,[p,f]=(0,a.useState)("0"),[g,k]=(0,a.useState)("0"),[S,P]=(0,a.useState)(!0),[C,A]=(0,a.useState)(!1),[E,U]=(0,a.useState)(!1),[F,L]=(0,a.useState)(""),[R,D]=(0,a.useState)([]),[O,I]=(0,a.useState)([]),[M,B]=(0,a.useState)([]),[_,z]=(0,a.useState)("all"),[W,H]=(0,a.useState)(!1),[q,Y]=(0,a.useState)(!1),V=(0,a.useRef)(null),$=(0,a.useRef)(null),G=(0,a.useRef)(null),X=(0,a.useRef)(null),K=l(r),J=r.replace("USDT",""),Q=()=>{switch(x){case"connected":default:return(0,s.jsx)(b.A,{className:"h-3 w-3"});case"connecting":return(0,s.jsx)(b.A,{className:"h-3 w-3 animate-pulse"});case"error":return(0,s.jsx)(y,{className:"h-3 w-3"})}},Z=()=>{switch(x){case"connected":default:return"text-emerald-500";case"connecting":return"text-blue-500";case"error":return"text-amber-500"}};(0,a.useEffect)(()=>{let e=async()=>{try{let e=await fetch("https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=".concat(r)),t=await e.json();t&&t.lastPrice&&(f(Number.parseFloat(t.lastPrice).toFixed(t.lastPrice>=1e3?0:t.lastPrice>=1?2:4)),k(t.priceChangePercent),P(Number.parseFloat(t.priceChangePercent)>=0))}catch(e){console.error("Error fetching price data:",e)}};e();let t=setInterval(e,5e3);return()=>clearInterval(t)},[r]),(0,a.useEffect)(()=>{(async()=>{if(C)try{H(!0);let e=d(),r=await fetch("https://fapi.binance.com/fapi/v1/ticker/24hr"),t=await r.json(),s=e.map(e=>{let r=t.find(r=>r.symbol===e);return r?{symbol:r.symbol,lastPrice:Number.parseFloat(r.lastPrice).toFixed(r.lastPrice>=1e3?0:r.lastPrice>=1?2:4),priceChangePercent:r.priceChangePercent}:null}).filter(Boolean).sort((r,t)=>{let s=e.indexOf(r.symbol),a=e.indexOf(t.symbol);return s-a});D(s),I(s),H(!1)}catch(e){console.error("Error fetching market data:",e),H(!1)}})()},[C]),(0,a.useEffect)(()=>{let e=localStorage.getItem("favoriteMarkets");e&&B(JSON.parse(e))},[]),(0,a.useEffect)(()=>{if(0===R.length)return;let e=[...R];F&&(e=e.filter(e=>e.symbol.toLowerCase().includes(F.toLowerCase()))),"favorites"===_&&(e=e.filter(e=>M.includes(e.symbol))),I(e)},[F,R,_,M]),(0,a.useEffect)(()=>{function e(e){V.current&&!V.current.contains(e.target)&&A(!1)}return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),(0,a.useEffect)(()=>{let e=e=>{$.current&&!$.current.contains(e.target)&&U(!1)};if(E)return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[E]),(0,a.useEffect)(()=>{let e=e=>{X.current&&!X.current.contains(e.target)&&Y(!1)};if(q)return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[q]),(0,a.useEffect)(()=>{C&&G.current&&setTimeout(()=>{var e;null===(e=G.current)||void 0===e||e.focus()},100)},[C]);let ee=e=>{t&&t(e),A(!1),L("")},er=(e,r)=>{r.stopPropagation();let t=M.includes(e)?M.filter(r=>r!==e):[...M,e];B(t),localStorage.setItem("favoriteMarkets",JSON.stringify(t))},et=()=>{L(""),G.current&&G.current.focus()};return(0,s.jsxs)("div",{className:"relative",ref:V,children:[(0,s.jsxs)("div",{className:"flex items-center justify-between bg-card rounded-lg border border-border p-2 sm:p-3",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsxs)("div",{className:"flex items-center cursor-pointer hover:bg-muted/50 rounded-md px-2 py-1 transition-colors",onClick:()=>A(!C),children:[(0,s.jsx)("span",{className:"text-base sm:text-lg font-bold mr-1",children:K}),(0,s.jsxs)("span",{className:"text-xs text-muted-foreground hidden sm:inline mr-1",children:[J,"/USDT"]}),(0,s.jsx)(u.A,{className:"h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground"})]}),(0,s.jsxs)("div",{className:"flex items-center ml-3 sm:ml-4",children:[(0,s.jsx)("span",{className:"text-sm sm:text-base font-medium mr-2",children:p}),(0,s.jsxs)("span",{className:"flex items-center text-xs ".concat(S?"text-emerald-500":"text-red-500"),children:[S?(0,s.jsx)(m.A,{className:"h-3 w-3 mr-0.5"}):(0,s.jsx)(u.A,{className:"h-3 w-3 mr-0.5"}),S?"+":"",g,"%"]}),(0,s.jsx)("div",{className:"sm:hidden ml-3",children:(0,s.jsx)(T,{})})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"hidden sm:block",children:(0,s.jsx)(T,{})}),(0,s.jsxs)("div",{className:"relative",ref:$,children:[(0,s.jsxs)("button",{onClick:()=>U(!E),className:"flex items-center bg-muted/30 hover:bg-muted/50 rounded-md px-2 py-1 transition-colors border border-border/50",children:[(0,s.jsx)(N,{className:"h-3 w-3 mr-1 text-muted-foreground"}),(0,s.jsxs)("span",{className:"text-xs font-medium text-primary",children:[n,"x"]})]}),E&&(0,s.jsx)("div",{className:"absolute top-full right-0 mt-2 w-64 bg-card rounded-lg shadow-lg border border-border z-50 animate-in fade-in slide-in-from-top-5 duration-200",children:(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,s.jsx)("h3",{className:"text-sm font-semibold",children:"Adjust Leverage"}),(0,s.jsx)("button",{onClick:()=>U(!1),className:"text-muted-foreground hover:text-foreground",children:(0,s.jsx)(v.A,{className:"h-4 w-4"})})]}),(0,s.jsx)("div",{className:"grid grid-cols-5 gap-2 mb-3",children:[5,10,20,50,100].map(e=>(0,s.jsxs)("button",{onClick:()=>{o&&o(e),U(!1)},className:"py-2 text-xs font-semibold rounded-lg transition-all duration-200 ".concat(n===e?"bg-primary text-primary-foreground shadow-md":"bg-muted/50 border border-border/50 hover:bg-muted hover:border-border text-muted-foreground hover:text-foreground"),children:[e,"x"]},e))}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("input",{type:"range",min:"1",max:"125",step:"1",value:n,onChange:e=>{o&&o(Number.parseInt(e.target.value))},className:"w-full h-2 bg-muted rounded-lg appearance-none cursor-pointer slider",style:{background:"linear-gradient(to right, hsl(var(--primary)) 0%, hsl(var(--primary)) ".concat((n-1)/124*100,"%, hsl(var(--muted)) ").concat((n-1)/124*100,"%, hsl(var(--muted)) 100%)")}}),(0,s.jsxs)("div",{className:"flex justify-between text-xs text-muted-foreground",children:[(0,s.jsx)("span",{children:"1x"}),(0,s.jsxs)("span",{className:"font-medium text-primary",children:[n,"x"]}),(0,s.jsx)("span",{children:"125x"})]})]})]})})]}),(0,s.jsx)("button",{onClick:()=>{c&&c(!i)},className:"flex items-center rounded-md px-2 py-1 transition-colors border text-xs font-medium ".concat(i?"bg-primary/10 border-primary/30 text-primary":"bg-muted/30 border-border/50 hover:bg-muted/50 text-muted-foreground"),children:"Cross"}),(0,s.jsxs)("div",{className:"relative",ref:X,children:[(0,s.jsx)("button",{onClick:()=>Y(!q),className:"flex items-center rounded-md p-1 transition-colors hover:bg-muted/50 ".concat(Z()),children:Q()}),q&&(0,s.jsx)("div",{className:"absolute top-full right-0 mt-2 w-64 bg-card rounded-lg shadow-lg border border-border z-50 animate-in fade-in slide-in-from-top-5 duration-200",children:(0,s.jsxs)("div",{className:"p-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,s.jsx)("div",{className:Z(),children:Q()}),(0,s.jsx)("span",{className:"text-sm font-semibold",children:"Connection Status"})]}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground mb-3",children:(()=>{switch(x){case"connected":default:return"Real-time data connected";case"connecting":return"Connecting to real-time data...";case"error":return"Using fallback price updates. Real-time data may be delayed."}})()}),"error"===x&&h&&(0,s.jsx)("button",{onClick:()=>{h(),Y(!1)},className:"w-full text-xs bg-amber-500/20 hover:bg-amber-500/30 text-amber-600 px-3 py-2 rounded-md transition-colors",children:"Retry Connection"})]})})]})]})]}),C&&(0,s.jsxs)("div",{className:"absolute left-0 right-0 sm:left-0 sm:right-auto mt-2 w-full sm:w-80 bg-card rounded-lg shadow-lg border border-border z-50 animate-in fade-in slide-in-from-top-5 duration-200",children:[(0,s.jsxs)("div",{className:"p-2 border-b border-border",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{ref:G,type:"text",placeholder:"Search markets...",value:F,onChange:e=>L(e.target.value),className:"w-full pl-8 pr-8 py-1.5 text-sm bg-background border border-border rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"}),(0,s.jsx)(j.A,{className:"absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),F&&(0,s.jsx)("button",{onClick:et,className:"absolute right-2 top-1/2 transform -translate-y-1/2",children:(0,s.jsx)(v.A,{className:"h-4 w-4 text-muted-foreground"})})]}),(0,s.jsxs)("div",{className:"flex mt-2 border border-border rounded-md overflow-hidden",children:[(0,s.jsx)("button",{className:"flex-1 py-1 text-xs font-medium ".concat("all"===_?"bg-primary text-primary-foreground":"bg-background"),onClick:()=>z("all"),children:"All Markets"}),(0,s.jsx)("button",{className:"flex-1 py-1 text-xs font-medium ".concat("favorites"===_?"bg-primary text-primary-foreground":"bg-background"),onClick:()=>z("favorites"),children:"Favorites"})]})]}),(0,s.jsx)("div",{className:"max-h-[350px] overflow-y-auto",children:W?(0,s.jsx)("div",{className:"flex justify-center py-8",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-primary"})}):0===O.length?(0,s.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,s.jsx)("p",{className:"text-sm",children:"No markets found"}),F&&(0,s.jsx)("button",{className:"mt-2 text-xs text-primary hover:underline",onClick:et,children:"Clear search"})]}):(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"grid grid-cols-[1fr,auto,auto] px-2 sm:px-3 py-1 border-b border-border text-xs text-muted-foreground",children:[(0,s.jsx)("div",{children:"Pair"}),(0,s.jsx)("div",{children:"Price"}),(0,s.jsx)("div",{className:"hidden sm:block",children:"24h"}),(0,s.jsx)("div",{className:"sm:hidden",children:"%"})]}),O.map(e=>(0,s.jsxs)("div",{className:"px-2 sm:px-3 py-2 hover:bg-muted cursor-pointer grid grid-cols-[1fr,auto,auto] items-center ".concat(e.symbol===r?"bg-primary/10":""),onClick:()=>ee(e.symbol),children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("button",{onClick:r=>er(e.symbol,r),className:"mr-1 focus:outline-none",children:(0,s.jsx)(w.A,{className:"h-3 w-3 ".concat(M.includes(e.symbol)?"fill-yellow-400 text-yellow-400":"text-muted-foreground")})}),(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("span",{className:"font-medium text-xs sm:text-sm",children:l(e.symbol)}),(0,s.jsx)("span",{className:"text-xs text-muted-foreground hidden sm:block",children:e.symbol.replace("USDT","")})]})]}),(0,s.jsx)("div",{className:"text-right text-xs sm:text-sm",children:e.lastPrice}),(0,s.jsxs)("div",{className:"text-right text-xs ml-1 sm:ml-3 ".concat(Number.parseFloat(e.priceChangePercent)>=0?"text-emerald-500":"text-red-500"),children:[Number.parseFloat(e.priceChangePercent)>=0?"+":"",e.priceChangePercent,"%"]})]},e.symbol))]})})]})]})}var A=t(1243),E=t(5196),U=t(16785);function F(){let{positions:e,isLoading:r,error:t,closePosition:n,updatePosition:o}=(0,h.fx)(),[l,d]=(0,a.useState)(null),[i,c]=(0,a.useState)(""),[m,u]=(0,a.useState)(""),[x,f]=(0,a.useState)(new Set),b=async r=>{if(!x.has(r))try{let t=e.find(e=>e.id===r);if(!t){p.oR.info("Position already closed",{description:"This position has already been closed"});return}f(e=>new Set(e).add(r)),await n(r),p.oR.success("Position closed successfully",{description:"".concat(t.symbol," ").concat(t.side," position closed")})}catch(r){console.error("Error closing position:",r);let e=r instanceof Error?r.message:"Unknown error";e.includes("not found")||e.includes("already been closed")?p.oR.info("Position already closed",{description:"This position has already been closed"}):p.oR.error("Failed to close position. Please try again.")}finally{f(e=>{let t=new Set(e);return t.delete(r),t})}},g=e=>{var r,t;d(e.id),c((null===(r=e.stopLoss)||void 0===r?void 0:r.toString())||""),u((null===(t=e.takeProfit)||void 0===t?void 0:t.toString())||"")},y=async r=>{try{let t=i?parseFloat(i):void 0,s=m?parseFloat(m):void 0;await o({id:r,stopLoss:t,takeProfit:s});let a=e.find(e=>e.id===r);p.oR.success("Position updated successfully",{description:"".concat(null==a?void 0:a.symbol," SL/TP updated")}),d(null),c(""),u("")}catch(e){console.error("Error updating position:",e),p.oR.error("Failed to update position. Please try again.")}};return r&&0===e.length?(0,s.jsx)("div",{className:"bg-card rounded-lg border border-border p-3",children:(0,s.jsx)("div",{className:"flex justify-center py-6",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-primary"})})}):t?(0,s.jsx)("div",{className:"bg-card rounded-lg border border-border p-3",children:(0,s.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4 flex items-center",children:[(0,s.jsx)(A.A,{className:"h-5 w-5 mr-2"}),(0,s.jsx)("span",{children:t})]})}):0===e.length?(0,s.jsx)("div",{className:"bg-card rounded-lg border border-border p-3",children:(0,s.jsx)("div",{className:"text-center py-6 text-muted-foreground text-sm",children:"No open positions"})}):(0,s.jsx)("div",{className:"bg-card rounded-lg border border-border p-3",children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full text-xs",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"border-b border-border",children:[(0,s.jsx)("th",{className:"text-left py-2 font-medium text-muted-foreground",children:"Symbol"}),(0,s.jsx)("th",{className:"text-left py-2 font-medium text-muted-foreground",children:"Side"}),(0,s.jsx)("th",{className:"text-right py-2 font-medium text-muted-foreground",children:"Size"}),(0,s.jsx)("th",{className:"text-right py-2 font-medium text-muted-foreground",children:"Entry Price"}),(0,s.jsx)("th",{className:"text-right py-2 font-medium text-muted-foreground",children:"Mark Price"}),(0,s.jsx)("th",{className:"text-right py-2 font-medium text-muted-foreground",children:"Leverage"}),(0,s.jsx)("th",{className:"text-right py-2 font-medium text-muted-foreground",children:"PnL"}),(0,s.jsx)("th",{className:"text-right py-2 font-medium text-muted-foreground",children:"SL/TP"}),(0,s.jsx)("th",{className:"text-right py-2 font-medium text-muted-foreground",children:"Liq. Price"}),(0,s.jsx)("th",{className:"text-center py-2 font-medium text-muted-foreground",children:"Actions"})]})}),(0,s.jsx)("tbody",{children:e.map(e=>(0,s.jsxs)("tr",{className:"border-b border-border/50 hover:bg-muted/30",children:[(0,s.jsx)("td",{className:"py-2 font-medium",children:e.symbol}),(0,s.jsx)("td",{className:"py-2 ".concat("LONG"===e.side?"text-emerald-500":"text-red-500"),children:e.side}),(0,s.jsx)("td",{className:"py-2 text-right",children:e.size.toFixed(4)}),(0,s.jsx)("td",{className:"py-2 text-right",children:e.entryPrice.toFixed(2)}),(0,s.jsx)("td",{className:"py-2 text-right",children:e.markPrice.toFixed(2)}),(0,s.jsxs)("td",{className:"py-2 text-right font-medium",children:[e.leverage,"x"]}),(0,s.jsxs)("td",{className:"py-2 text-right ".concat(e.pnl>=0?"text-emerald-500":"text-red-500"),children:[e.pnl>=0?"+":"",e.pnl.toFixed(2)," (",e.pnlPercent>=0?"+":"",e.pnlPercent.toFixed(2),"%)"]}),(0,s.jsx)("td",{className:"py-2 text-right",children:l===e.id?(0,s.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,s.jsx)("input",{type:"text",value:i,onChange:e=>c(e.target.value),placeholder:"Stop Loss",className:"w-24 p-1 text-xs border border-border rounded"}),(0,s.jsx)("input",{type:"text",value:m,onChange:e=>u(e.target.value),placeholder:"Take Profit",className:"w-24 p-1 text-xs border border-border rounded"})]}):(0,s.jsxs)("div",{className:"text-xs",children:[(0,s.jsx)("div",{className:"text-red-500",children:e.stopLoss?"SL: ".concat(e.stopLoss.toFixed(2)):"No SL"}),(0,s.jsx)("div",{className:"text-emerald-500",children:e.takeProfit?"TP: ".concat(e.takeProfit.toFixed(2)):"No TP"})]})}),(0,s.jsx)("td",{className:"py-2 text-right",children:e.liquidationPrice.toFixed(2)}),(0,s.jsx)("td",{className:"py-2 text-center",children:(0,s.jsxs)("div",{className:"flex justify-center space-x-1",children:[l===e.id?(0,s.jsx)("button",{onClick:()=>y(e.id),className:"p-1 rounded-md hover:bg-muted",title:"Save",children:(0,s.jsx)(E.A,{className:"h-3.5 w-3.5 text-emerald-500"})}):(0,s.jsx)("button",{onClick:()=>g(e),className:"p-1 rounded-md hover:bg-muted",title:"Edit SL/TP",children:(0,s.jsx)(U.A,{className:"h-3.5 w-3.5 text-muted-foreground"})}),(0,s.jsx)("button",{onClick:()=>b(e.id),className:"p-1 rounded-md hover:bg-muted ".concat(x.has(e.id)?"opacity-50 cursor-not-allowed":""),title:"Close Position",disabled:x.has(e.id),children:x.has(e.id)?(0,s.jsx)("div",{className:"h-3.5 w-3.5 border border-muted-foreground border-t-transparent rounded-full animate-spin"}):(0,s.jsx)(v.A,{className:"h-3.5 w-3.5 text-muted-foreground"})})]})})]},e.id))})]})})})}function L(){let{orders:e,isLoading:r,cancelOrder:t}=(0,h.fx)(),a=async r=>{try{let s=e.find(e=>e.id===r);await t(r),s&&p.oR.success("Order cancelled successfully",{description:"".concat(s.symbol," ").concat(s.side," order cancelled")})}catch(e){console.error("Error canceling order:",e),p.oR.error("Failed to cancel order. Please try again.")}},n=e=>new Date(e).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"});return r?(0,s.jsx)("div",{className:"bg-card rounded-lg border border-border p-3",children:(0,s.jsx)("div",{className:"flex justify-center py-6",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-primary"})})}):0===e.length?(0,s.jsx)("div",{className:"bg-card rounded-lg border border-border p-3",children:(0,s.jsx)("div",{className:"text-center py-6 text-muted-foreground text-sm",children:"No open orders"})}):(0,s.jsx)("div",{className:"bg-card rounded-lg border border-border p-3",children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full text-xs",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"border-b border-border",children:[(0,s.jsx)("th",{className:"text-left py-2 font-medium text-muted-foreground",children:"Symbol"}),(0,s.jsx)("th",{className:"text-left py-2 font-medium text-muted-foreground",children:"Type"}),(0,s.jsx)("th",{className:"text-left py-2 font-medium text-muted-foreground",children:"Side"}),(0,s.jsx)("th",{className:"text-left py-2 font-medium text-muted-foreground",children:"Price"}),(0,s.jsx)("th",{className:"text-left py-2 font-medium text-muted-foreground",children:"Amount"}),(0,s.jsx)("th",{className:"text-left py-2 font-medium text-muted-foreground",children:"Leverage"}),(0,s.jsx)("th",{className:"text-left py-2 font-medium text-muted-foreground",children:"SL/TP"}),(0,s.jsx)("th",{className:"text-left py-2 font-medium text-muted-foreground",children:"Time"}),(0,s.jsx)("th",{className:"text-right py-2 font-medium text-muted-foreground",children:"Actions"})]})}),(0,s.jsx)("tbody",{children:e.map(e=>(0,s.jsxs)("tr",{className:"border-b border-border/50 hover:bg-muted/30",children:[(0,s.jsx)("td",{className:"py-2",children:(0,s.jsx)("span",{className:"font-medium",children:e.symbol})}),(0,s.jsx)("td",{className:"py-2",children:e.type}),(0,s.jsx)("td",{className:"py-2",children:(0,s.jsx)("span",{className:"BUY"===e.side?"text-emerald-500":"text-red-500",children:e.side})}),(0,s.jsx)("td",{className:"py-2",children:e.price.toFixed(2)}),(0,s.jsx)("td",{className:"py-2",children:e.origQty.toFixed(4)}),(0,s.jsxs)("td",{className:"py-2 font-medium",children:[e.leverage,"x"]}),(0,s.jsx)("td",{className:"py-2",children:(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"text-red-500 mr-1",children:"SL:"}),(0,s.jsx)("span",{children:e.stopLoss?e.stopLoss.toFixed(2):"-"})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"text-emerald-500 mr-1",children:"TP:"}),(0,s.jsx)("span",{children:e.takeProfit?e.takeProfit.toFixed(2):"-"})]})]})}),(0,s.jsx)("td",{className:"py-2",children:n(e.timestamp)}),(0,s.jsx)("td",{className:"py-2 text-right",children:(0,s.jsx)("button",{onClick:()=>a(e.id),className:"p-1 rounded-md hover:bg-muted",title:"Cancel Order",children:(0,s.jsx)(v.A,{className:"h-3.5 w-3.5"})})})]},e.id))})]})})})}function R(){let{trades:e,isLoading:r}=(0,h.fx)(),t=e=>new Date(e).toLocaleString([],{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return r?(0,s.jsx)("div",{className:"bg-card rounded-lg border border-border p-3",children:(0,s.jsx)("div",{className:"flex justify-center py-6",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-primary"})})}):0===e.length?(0,s.jsx)("div",{className:"bg-card rounded-lg border border-border p-3",children:(0,s.jsx)("div",{className:"text-center py-6 text-muted-foreground text-sm",children:"No trade history"})}):(0,s.jsx)("div",{className:"bg-card rounded-lg border border-border p-3",children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full text-xs",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"border-b border-border",children:[(0,s.jsx)("th",{className:"text-left py-2 font-medium text-muted-foreground",children:"Time"}),(0,s.jsx)("th",{className:"text-left py-2 font-medium text-muted-foreground",children:"Symbol"}),(0,s.jsx)("th",{className:"text-left py-2 font-medium text-muted-foreground",children:"Side"}),(0,s.jsx)("th",{className:"text-left py-2 font-medium text-muted-foreground",children:"Price"}),(0,s.jsx)("th",{className:"text-left py-2 font-medium text-muted-foreground",children:"Quantity"}),(0,s.jsx)("th",{className:"text-left py-2 font-medium text-muted-foreground",children:"Leverage"}),(0,s.jsx)("th",{className:"text-left py-2 font-medium text-muted-foreground",children:"Fee"}),(0,s.jsx)("th",{className:"text-right py-2 font-medium text-muted-foreground",children:"Realized PnL"})]})}),(0,s.jsx)("tbody",{children:e.map(e=>(0,s.jsxs)("tr",{className:"border-b border-border/50 hover:bg-muted/30",children:[(0,s.jsx)("td",{className:"py-2",children:t(e.timestamp)}),(0,s.jsx)("td",{className:"py-2",children:(0,s.jsx)("span",{className:"font-medium",children:e.symbol})}),(0,s.jsx)("td",{className:"py-2",children:(0,s.jsx)("span",{className:"BUY"===e.side?"text-emerald-500":"text-red-500",children:e.side})}),(0,s.jsx)("td",{className:"py-2",children:e.price.toFixed(2)}),(0,s.jsx)("td",{className:"py-2",children:e.quantity.toFixed(4)}),(0,s.jsxs)("td",{className:"py-2 font-medium",children:[e.leverage,"x"]}),(0,s.jsxs)("td",{className:"py-2",children:[e.commission.toFixed(2)," USD"]}),(0,s.jsx)("td",{className:"py-2 text-right",children:(0,s.jsxs)("span",{className:e.realizedPnl>0?"text-emerald-500":e.realizedPnl<0?"text-red-500":"",children:[e.realizedPnl>0?"+":"",e.realizedPnl.toFixed(2)," USD"]})})]},e.id))})]})})})}var D=t(97168),O=t(53999);let I=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,O.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});I.displayName="Card";let M=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,O.cn)("flex flex-col space-y-1.5 p-6",t),...a})});M.displayName="CardHeader";let B=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,O.cn)("text-2xl font-semibold leading-none tracking-tight",t),...a})});B.displayName="CardTitle",a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,O.cn)("text-sm text-muted-foreground",t),...a})}).displayName="CardDescription";let _=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,O.cn)("p-6 pt-0",t),...a})});_.displayName="CardContent",a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,O.cn)("flex items-center p-6 pt-0",t),...a})}).displayName="CardFooter";var z=t(88145);function W(){var e;let{positions:r,orders:t,trades:n,marketData:o,placeOrder:l,closePosition:d,cancelOrder:i,getTotalPnL:c,getTotalMargin:m,state:u}=(0,h.fx)(),{user:x}=(0,k.A)(),[f,b]=(0,a.useState)(null),[g,y]=(0,a.useState)(!1),[N,v]=(0,a.useState)(!1),[j,w]=(0,a.useState)("");(0,a.useEffect)(()=>P.A.subscribe(e=>{b(e)}),[]);let S=async()=>{if(!x||!f){p.oR.error("Please sign in to test order placement");return}v(!0),w("");try{var e;console.log("\uD83E\uDDEA Testing market order placement..."),console.log("User:",f),console.log("User balance:",null===(e=f.balance)||void 0===e?void 0:e.current);let r={symbol:"BTCUSDT",side:"BUY",type:"MARKET",quantity:.001,leverage:10};console.log("Order request:",r);let t=await l(r);w("✅ Market order placed successfully! Order ID: ".concat(t)),console.log("✅ Test order successful:",t),p.oR.success("Test market order placed successfully!",{description:"Order ID: ".concat(t),duration:5e3})}catch(e){console.error("❌ Test order failed:",e),w("❌ Order failed: ".concat(e instanceof Error?e.message:"Unknown error")),p.oR.error("Test order failed",{description:e instanceof Error?e.message:"Unknown error",duration:8e3})}finally{v(!1)}},T=async()=>{if(!x||!f){p.oR.error("Please sign in to test order placement");return}v(!0),w("");try{var e;console.log("\uD83E\uDDEA Testing limit order placement...");let r=(null===(e=o.BTCUSDT)||void 0===e?void 0:e.price)||43e3,t={symbol:"BTCUSDT",side:"BUY",type:"LIMIT",quantity:.001,price:.98*r,leverage:10,stopLoss:.95*r,takeProfit:1.05*r};console.log("Limit order request:",t);let s=await l(t);w("✅ Limit order placed successfully! Order ID: ".concat(s)),console.log("✅ Test limit order successful:",s),p.oR.success("Test limit order placed successfully!",{description:"Order ID: ".concat(s),duration:5e3})}catch(e){console.error("❌ Test limit order failed:",e),w("❌ Limit order failed: ".concat(e instanceof Error?e.message:"Unknown error")),p.oR.error("Test limit order failed",{description:e instanceof Error?e.message:"Unknown error",duration:8e3})}finally{v(!1)}};return g?(0,s.jsxs)(I,{className:"fixed bottom-4 right-4 w-96 max-h-96 overflow-y-auto z-50 bg-background border shadow-lg",children:[(0,s.jsx)(M,{className:"pb-2",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)(B,{className:"text-sm",children:"Trading Debug Panel"}),(0,s.jsx)(D.$,{onClick:()=>y(!1),variant:"ghost",size:"sm",children:"\xd7"})]})}),(0,s.jsxs)(_,{className:"space-y-3 text-xs",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("div",{className:"text-muted-foreground font-medium",children:"Authentication Status"}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{children:"User ID:"}),(0,s.jsx)("span",{className:(null==f?void 0:f.id)?"text-green-500":"text-red-500",children:(null==f?void 0:f.id)?"✓ Authenticated":"✗ Not authenticated"})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{children:"Balance:"}),(0,s.jsxs)("span",{children:[(null==f?void 0:null===(e=f.balance)||void 0===e?void 0:e.current)||0," USDT"]})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{children:"Trading State:"}),(0,s.jsx)("span",{className:u.error?"text-red-500":"text-green-500",children:u.isLoading?"Loading...":u.error?"Error":"Ready"})]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-muted-foreground",children:"Total PnL"}),(0,s.jsxs)("div",{className:c()>=0?"text-green-500":"text-red-500",children:["$",c().toFixed(2)]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-muted-foreground",children:"Total Margin"}),(0,s.jsxs)("div",{children:["$",m().toFixed(2)]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("div",{className:"text-muted-foreground font-medium",children:"Quick Actions"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(D.$,{onClick:S,size:"sm",variant:"outline",disabled:N||!x||!f,children:N?"Testing...":"Test Market Order"}),(0,s.jsx)(D.$,{onClick:T,size:"sm",variant:"outline",disabled:N||!x||!f,children:N?"Testing...":"Test Limit Order"})]}),j&&(0,s.jsx)("div",{className:"mt-2 p-2 bg-muted rounded text-xs",children:j})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("div",{className:"text-muted-foreground font-medium",children:"Current State"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)(z.E,{variant:"secondary",children:["Positions: ",r.length]}),(0,s.jsxs)(z.E,{variant:"secondary",children:["Orders: ",t.length]}),(0,s.jsxs)(z.E,{variant:"secondary",children:["Trades: ",n.length]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("div",{className:"text-muted-foreground font-medium",children:"Market Data"}),(0,s.jsx)("div",{className:"space-y-1",children:Object.entries(o).slice(0,3).map(e=>{let[r,t]=e;return(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{children:r}),(0,s.jsxs)("span",{className:t.priceChangePercent>=0?"text-green-500":"text-red-500",children:["$",t.price.toFixed(2)," (",t.priceChangePercent.toFixed(2),"%)"]})]},r)})})]}),r.length>0&&(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("div",{className:"text-muted-foreground font-medium",children:"Active Positions"}),(0,s.jsx)("div",{className:"space-y-1",children:r.slice(0,3).map(e=>(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium",children:e.symbol}),(0,s.jsx)("span",{className:"ml-1 ".concat("LONG"===e.side?"text-green-500":"text-red-500"),children:e.side})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)("span",{className:e.pnl>=0?"text-green-500":"text-red-500",children:["$",e.pnl.toFixed(2)]}),(0,s.jsx)(D.$,{onClick:()=>d(e.id),size:"sm",variant:"ghost",className:"h-6 w-6 p-0",children:"\xd7"})]})]},e.id))})]}),t.length>0&&(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("div",{className:"text-muted-foreground font-medium",children:"Open Orders"}),(0,s.jsx)("div",{className:"space-y-1",children:t.slice(0,3).map(e=>(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium",children:e.symbol}),(0,s.jsx)("span",{className:"ml-1 ".concat("BUY"===e.side?"text-green-500":"text-red-500"),children:e.side}),(0,s.jsx)("span",{className:"ml-1 text-muted-foreground",children:e.type})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)("span",{children:["$",e.price.toFixed(2)]}),(0,s.jsx)(D.$,{onClick:()=>i(e.id),size:"sm",variant:"ghost",className:"h-6 w-6 p-0",children:"\xd7"})]})]},e.id))})]})]})]}):(0,s.jsx)(D.$,{onClick:()=>y(!0),className:"fixed bottom-4 right-4 z-50",variant:"outline",children:"Debug Panel"})}function H(){let{updateMarketData:e,positions:r,orders:t,trades:n,getTotalPnL:o}=(0,h.fx)(),[l,d]=(0,a.useState)(!1),[i,m]=(0,a.useState)("BTCUSDT"),[u,x]=(0,a.useState)("0"),[p,b]=(0,a.useState)("positions"),[g,y]=(0,a.useState)(!1),[N,v]=(0,a.useState)("disconnected"),[j,w]=(0,a.useState)(!1),[k,S]=(0,a.useState)(10),[P,T]=(0,a.useState)(!0),A=(0,a.useRef)(null),E=(0,a.useRef)(null),U=(0,a.useRef)(0),D=()=>r.reduce((e,r)=>e+r.pnl,0),O=()=>n.reduce((e,r)=>e+r.realizedPnl,0),I=e=>{let r=e>0?"+":"";return"".concat(r).concat(e.toFixed(2)," USD")};(0,a.useEffect)(()=>{d(!0)},[]);let M=(0,a.useCallback)(async()=>{try{let r=new AbortController,t=setTimeout(()=>r.abort(),5e3),s=await fetch("https://fapi.binance.com/fapi/v1/ticker/24hr?symbol=".concat(i),{signal:r.signal});if(clearTimeout(t),!s.ok)throw Error("HTTP error! status: ".concat(s.status));let a=await s.json();if(a&&a.lastPrice){let r=Number.parseFloat(a.lastPrice);x(r.toFixed(r>=1e3?0:r>=1?2:4)),e(i,{price:r,priceChange:Number.parseFloat(a.priceChange||"0"),priceChangePercent:Number.parseFloat(a.priceChangePercent||"0"),volume:Number.parseFloat(a.volume||"0")})}}catch(e){"AbortError"===e.name?console.warn("Price data fetch timeout for",i):console.warn("Error fetching price data via REST:",e.message)}},[i,e]),B=(0,a.useCallback)(()=>{if(E.current&&(clearTimeout(E.current),E.current=null),A.current&&A.current.readyState===WebSocket.OPEN)try{A.current.close()}catch(e){console.error("Error closing existing WebSocket:",e)}try{v("connecting");let r=new WebSocket("wss://fstream.binance.com/ws");A.current=r;let t=setTimeout(()=>{r.readyState===WebSocket.CONNECTING&&(console.warn("WebSocket connection timeout, falling back to REST API"),r.close(),v("error"),M())},1e4);r.addEventListener("open",()=>{clearTimeout(t)}),r.onopen=()=>{console.log("WebSocket connected"),v("connected"),U.current=0;try{let e=JSON.stringify({method:"SUBSCRIBE",params:["".concat(i.toLowerCase(),"@ticker")],id:1});r.send(e)}catch(e){console.error("Error sending subscription message:",e)}},r.onmessage=r=>{try{let t=JSON.parse(r.data);if("24hrTicker"===t.e){let r=Number.parseFloat(t.c),s=r.toFixed(r>=1e3?0:r>=1?2:4);x(s),e(i,{price:r,priceChange:Number.parseFloat(t.P),priceChangePercent:Number.parseFloat(t.P),volume:Number.parseFloat(t.v)})}}catch(e){console.error("Error processing WebSocket message:",e)}},r.onerror=e=>{console.warn("WebSocket connection failed, using REST API fallback:",e),v("error"),M();let r=setInterval(M,5e3);A.current||(A.current={intervalId:r})},r.onclose=e=>{if(console.log("WebSocket connection closed:",e),v("disconnected"),U.current<5)U.current+=1,console.log("Attempting to reconnect (".concat(U.current,"/").concat(5,")...")),E.current=setTimeout(()=>{B()},3e3);else{console.log("Max reconnect attempts reached. Using REST API fallback.");let e=setInterval(M,5e3);return()=>clearInterval(e)}}}catch(e){console.warn("Failed to initialize WebSocket, using REST API fallback:",e),v("error"),M()}},[i,M]);(0,a.useEffect)(()=>(M(),B(),()=>{if(E.current&&clearTimeout(E.current),A.current)try{if(A.current.readyState===WebSocket.OPEN){let e=JSON.stringify({method:"UNSUBSCRIBE",params:["".concat(i.toLowerCase(),"@ticker")],id:2});A.current.send(e)}A.current.close()}catch(e){console.error("Error during WebSocket cleanup:",e)}}),[B,M,i]);let _=async e=>{console.log("Order submitted via trade page:",e)};return(0,a.useEffect)(()=>{if("connected"!==N){let e=setInterval(M,5e3);return()=>clearInterval(e)}},[N,M]),(0,s.jsxs)("div",{className:"h-full bg-background",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 xl:grid-cols-5 gap-3 h-full p-3",children:[(0,s.jsxs)("div",{className:"lg:col-span-3 xl:col-span-4 space-y-3",children:[(0,s.jsx)("div",{children:(0,s.jsx)(C,{symbol:i,onSymbolChange:e=>{if(m(e),M(),A.current&&A.current.readyState===WebSocket.OPEN)try{let r=JSON.stringify({method:"UNSUBSCRIBE",params:["".concat(i.toLowerCase(),"@ticker")],id:2});A.current.send(r);let t=JSON.stringify({method:"SUBSCRIBE",params:["".concat(e.toLowerCase(),"@ticker")],id:1});A.current.send(t)}catch(e){console.error("Error updating WebSocket subscription:",e),B()}else B()},leverage:k,onLeverageChange:S,isCrossMargin:P,onCrossMarginChange:T,connectionStatus:N,onRetryConnection:B})}),(0,s.jsx)("div",{className:"h-[400px] sm:h-[500px] lg:h-[600px]",children:(0,s.jsx)(c,{symbol:i,interval:"15"})}),(0,s.jsx)("div",{className:"mb-2 sm:mb-3",children:(0,s.jsxs)("div",{className:"flex border-b border-border overflow-x-auto",children:[(0,s.jsx)("button",{className:"px-3 sm:px-4 py-2 text-xs sm:text-sm font-medium whitespace-nowrap ".concat("positions"===p?"border-b-2 border-primary text-primary":"text-muted-foreground"),onClick:()=>b("positions"),children:(0,s.jsxs)("div",{className:"flex flex-col items-start",children:[(0,s.jsx)("span",{children:"Positions"}),r.length>0&&(0,s.jsx)("span",{className:"text-xs ".concat(D()>=0?"text-emerald-500":"text-red-500"),children:I(D())})]})}),(0,s.jsx)("button",{className:"px-3 sm:px-4 py-2 text-xs sm:text-sm font-medium whitespace-nowrap ".concat("orders"===p?"border-b-2 border-primary text-primary":"text-muted-foreground"),onClick:()=>b("orders"),children:(0,s.jsxs)("div",{className:"flex flex-col items-start",children:[(0,s.jsx)("span",{className:"hidden sm:inline",children:"Open Orders"}),(0,s.jsx)("span",{className:"sm:hidden",children:"Orders"}),t.length>0&&(0,s.jsxs)("span",{className:"text-xs text-muted-foreground",children:[t.length," order",1!==t.length?"s":""]})]})}),(0,s.jsx)("button",{className:"px-3 sm:px-4 py-2 text-xs sm:text-sm font-medium whitespace-nowrap ".concat("history"===p?"border-b-2 border-primary text-primary":"text-muted-foreground"),onClick:()=>b("history"),children:(0,s.jsxs)("div",{className:"flex flex-col items-start",children:[(0,s.jsx)("span",{className:"hidden sm:inline",children:"Trade History"}),(0,s.jsx)("span",{className:"sm:hidden",children:"History"}),n.length>0&&(0,s.jsx)("span",{className:"text-xs ".concat(O()>=0?"text-emerald-500":"text-red-500"),children:I(O())})]})})]})}),(0,s.jsxs)("div",{children:["positions"===p&&(0,s.jsx)(F,{}),"orders"===p&&(0,s.jsx)(L,{}),"history"===p&&(0,s.jsx)(R,{})]}),(0,s.jsx)(W,{})]}),(0,s.jsx)("div",{className:"hidden lg:block lg:col-span-1 xl:col-span-1",children:(0,s.jsx)("div",{className:"h-[calc(100vh-120px)] w-full max-w-[350px] lg:max-w-none mx-auto sticky top-4",children:(0,s.jsx)(f,{symbol:i,lastPrice:u,leverage:k,onOrderSubmit:_})})})]}),j&&(0,s.jsx)("div",{className:"lg:hidden fixed inset-0 z-50 bg-black/50 backdrop-blur-sm",onClick:()=>w(!1),children:(0,s.jsx)("div",{className:"absolute bottom-0 left-0 right-0 bg-background rounded-t-2xl border-t border-border max-h-[90vh] overflow-y-auto",onClick:e=>e.stopPropagation(),children:(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,s.jsx)("h2",{className:"text-lg font-bold",children:"Place Order"}),(0,s.jsx)("button",{onClick:()=>w(!1),className:"p-2 rounded-full hover:bg-muted",children:(0,s.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,s.jsx)(f,{symbol:i,lastPrice:u,leverage:k,onOrderSubmit:e=>{_(e),w(!1)}})]})})}),(0,s.jsxs)("button",{onClick:()=>w(!0),className:"lg:hidden fixed bottom-6 right-6 z-40 bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 text-white rounded-full p-4 shadow-xl shadow-emerald-600/30 transition-all duration-300 hover:scale-110 active:scale-95 group",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("svg",{className:"w-5 h-5 group-hover:rotate-90 transition-transform duration-300",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2.5,d:"M12 4v16m8-8H4"})}),(0,s.jsx)("span",{className:"text-sm font-bold tracking-wide",children:"TRADE"})]}),(0,s.jsx)("div",{className:"absolute inset-0 rounded-full bg-emerald-600 animate-ping opacity-20"}),(0,s.jsxs)("div",{className:"absolute bottom-full right-0 mb-2 px-3 py-1 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap",children:["Place Order",(0,s.jsx)("div",{className:"absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"})]})]})]})}function q(){return(0,s.jsx)(H,{})}},88145:(e,r,t)=>{"use strict";t.d(r,{E:()=>l});var s=t(95155);t(12115);var a=t(74466),n=t(53999);let o=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:r,variant:t,...a}=e;return(0,s.jsx)("div",{className:(0,n.cn)(o({variant:t}),r),...a})}},97168:(e,r,t)=>{"use strict";t.d(r,{$:()=>i});var s=t(95155),a=t(12115),n=t(99708),o=t(74466),l=t(53999);let d=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),i=a.forwardRef((e,r)=>{let{className:t,variant:a,size:o,asChild:i=!1,...c}=e,m=i?n.DX:"button";return(0,s.jsx)(m,{className:(0,l.cn)(d({variant:a,size:o,className:t})),ref:r,...c})});i.displayName="Button"},97636:(e,r,t)=>{"use strict";t.d(r,{D:()=>l,N:()=>o});var s=t(95155),a=t(12115);let n=(0,a.createContext)(void 0);function o(e){let{children:r}=e,[t,o]=(0,a.useState)("light"),[l,d]=(0,a.useState)("emerald"),[i,c]=(0,a.useState)("light"),[m,u]=(0,a.useState)(!1);return((0,a.useEffect)(()=>{u(!0);let e=localStorage.getItem("theme"),r=localStorage.getItem("colorScheme");e&&o(e),r&&d(r)},[]),(0,a.useEffect)(()=>{m&&(localStorage.setItem("theme",t),localStorage.setItem("colorScheme",l),"dark"===t||"system"===t&&window.matchMedia("(prefers-color-scheme: dark)").matches?(document.documentElement.classList.add("dark"),c("dark")):(document.documentElement.classList.remove("dark"),c("light")),document.documentElement.setAttribute("data-color-scheme",l))},[t,l,m]),(0,a.useEffect)(()=>{if(!m||"system"!==t)return;let e=window.matchMedia("(prefers-color-scheme: dark)"),r=()=>{e.matches?(document.documentElement.classList.add("dark"),c("dark")):(document.documentElement.classList.remove("dark"),c("light"))};return e.addEventListener("change",r),()=>e.removeEventListener("change",r)},[t,m]),m)?(0,s.jsx)(n.Provider,{value:{theme:t,setTheme:o,colorScheme:l,setColorScheme:d,resolvedTheme:i},children:r}):(0,s.jsx)(s.Fragment,{children:r})}function l(){let e=(0,a.useContext)(n);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}},99708:(e,r,t)=>{"use strict";t.d(r,{DX:()=>o});var s=t(12115),a=t(6101),n=t(95155),o=s.forwardRef((e,r)=>{let{children:t,...a}=e,o=s.Children.toArray(t),d=o.find(i);if(d){let e=d.props.children,t=o.map(r=>r!==d?r:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,n.jsx)(l,{...a,ref:r,children:s.isValidElement(e)?s.cloneElement(e,void 0,t):null})}return(0,n.jsx)(l,{...a,ref:r,children:t})});o.displayName="Slot";var l=s.forwardRef((e,r)=>{let{children:t,...n}=e;if(s.isValidElement(t)){let e=function(e){let r=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,t=r&&"isReactWarning"in r&&r.isReactWarning;return t?e.ref:(t=(r=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(t);return s.cloneElement(t,{...function(e,r){let t={...r};for(let s in r){let a=e[s],n=r[s];/^on[A-Z]/.test(s)?a&&n?t[s]=(...e)=>{n(...e),a(...e)}:a&&(t[s]=a):"style"===s?t[s]={...a,...n}:"className"===s&&(t[s]=[a,n].filter(Boolean).join(" "))}return{...e,...t}}(n,t.props),ref:r?(0,a.t)(r,e):e})}return s.Children.count(t)>1?s.Children.only(null):null});l.displayName="SlotClone";var d=({children:e})=>(0,n.jsx)(n.Fragment,{children:e});function i(e){return s.isValidElement(e)&&e.type===d}}},e=>{var r=r=>e(e.s=r);e.O(0,[992,965,879,55,776,475,135,441,684,358],()=>r(43876)),_N_E=e.O()}]);