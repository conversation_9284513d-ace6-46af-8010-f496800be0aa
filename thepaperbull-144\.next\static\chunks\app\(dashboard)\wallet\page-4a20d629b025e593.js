(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[170],{51873:(e,t,s)=>{"use strict";s.d(t,{E$:()=>b,NotificationContainer:()=>p});var r=s(95155),a=s(12115),n=s(40646),d=s(85339),i=s(1243),l=s(81284),o=s(54416),c=s(53999);let m={success:n.A,error:d.A,warning:i.A,info:l.A},x={success:"border-green-200 bg-green-50 text-green-900 dark:border-green-800 dark:bg-green-950 dark:text-green-100",error:"border-red-200 bg-red-50 text-red-900 dark:border-red-800 dark:bg-red-950 dark:text-red-100",warning:"border-yellow-200 bg-yellow-50 text-yellow-900 dark:border-yellow-800 dark:bg-yellow-950 dark:text-yellow-100",info:"border-blue-200 bg-blue-50 text-blue-900 dark:border-blue-800 dark:bg-blue-950 dark:text-blue-100"},u={"top-right":"top-4 right-4","top-left":"top-4 left-4","bottom-right":"bottom-4 right-4","bottom-left":"bottom-4 left-4","top-center":"top-4 left-1/2 transform -translate-x-1/2","bottom-center":"bottom-4 left-1/2 transform -translate-x-1/2"};function f(e){let{id:t,type:s="info",title:n,message:d,duration:i=4e3,onClose:l,closable:f=!0,position:g="top-right"}=e,[h,b]=(0,a.useState)(!0),[p,j]=(0,a.useState)(!1),N=m[s];(0,a.useEffect)(()=>{if(i>0){let e=setTimeout(()=>{v()},i);return()=>clearTimeout(e)}},[i]);let v=()=>{j(!0),setTimeout(()=>{b(!1),null==l||l()},300)};return h?(0,r.jsx)("div",{className:(0,c.cn)("fixed z-50 w-full max-w-sm p-4 border rounded-lg shadow-lg backdrop-blur-sm","transition-all duration-300 ease-in-out",!p&&"animate-slideInFromTop",p&&"animate-slideOutToRight",x[s],u[g]),style:{animation:p?"slideOutToRight 0.3s ease-in":"slideInFromTop 0.3s ease-out"},role:"alert","aria-live":"polite",children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)(N,{className:"h-5 w-5 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[n&&(0,r.jsx)("div",{className:"font-medium text-sm mb-1",children:n}),(0,r.jsx)("div",{className:"text-sm",children:d})]}),f&&(0,r.jsx)("button",{onClick:v,className:(0,c.cn)("flex-shrink-0 p-1 rounded-md transition-colors duration-200","hover:bg-black/10 dark:hover:bg-white/10","focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-current","text-current/70 hover:text-current"),"aria-label":"Close notification",children:(0,r.jsx)(o.A,{className:"h-4 w-4"})})]})}):null}class g{generateId(){return"notification-".concat(Date.now(),"-").concat(Math.random().toString(36).substr(2,9))}notify(){this.listeners.forEach(e=>e())}show(e){let t=this.generateId(),s={...e,id:t,onClose:()=>this.remove(t)};return this.notifications.set(t,s),this.notify(),t}remove(e){this.notifications.delete(e),this.notify()}clear(){this.notifications.clear(),this.notify()}getAll(){return Array.from(this.notifications.values())}subscribe(e){return this.listeners.add(e),()=>this.listeners.delete(e)}success(e,t){return this.show({...t,type:"success",message:e})}error(e,t){return this.show({...t,type:"error",message:e})}warning(e,t){return this.show({...t,type:"warning",message:e})}info(e,t){return this.show({...t,type:"info",message:e})}constructor(){this.notifications=new Map,this.listeners=new Set}}let h=new g;function b(){let[e,t]=(0,a.useState)([]);return(0,a.useEffect)(()=>{let e=()=>{t(h.getAll())};return e(),h.subscribe(e)},[]),{notifications:e,show:h.show.bind(h),remove:h.remove.bind(h),clear:h.clear.bind(h),success:h.success.bind(h),error:h.error.bind(h),warning:h.warning.bind(h),info:h.info.bind(h)}}function p(){let{notifications:e}=b();return(0,r.jsx)(r.Fragment,{children:e.map(e=>(0,r.jsx)(f,{...e},e.id))})}},53999:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var r=s(52596),a=s(39688);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.QP)((0,r.$)(t))}},54493:(e,t,s)=>{Promise.resolve().then(s.bind(s,93486))},88145:(e,t,s)=>{"use strict";s.d(t,{E:()=>i});var r=s(95155);s(12115);var a=s(74466),n=s(53999);let d=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:s,...a}=e;return(0,r.jsx)("div",{className:(0,n.cn)(d({variant:s}),t),...a})}},93486:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>K});var r=s(95155),a=s(12115),n=s(39785),d=s(91788),i=s(53904),l=s(84616),o=s(76356),c=s(17951),m=s(92657),x=s(78749),u=s(44020),f=s(33109),g=s(68500),h=s(34135),b=s(50475),p=s(88145);function j(){var e;let[t,s]=(0,a.useState)(!0),[n,d]=(0,a.useState)("1W"),[i,l]=(0,a.useState)(null),{accountInfo:o,positions:j,getTotalPnL:N,getTotalMargin:v,getAvailableBalance:y}=(0,h.fx)();(0,a.useEffect)(()=>{let e=b.A.subscribe(e=>{l(e)});return l(b.A.getUser()),e},[]);let w=(null==i?void 0:null===(e=i.balance)||void 0===e?void 0:e.current)||1e4,A=(null==o?void 0:o.totalWalletBalance)||w,S=N()||0,T=v()||0,k=y();(null==k||0===k)&&(k=Math.max(0,A-T+S));let C=j.length,F=A>0?S/A*100:0,L=S>=0?"up":"down",P=[{name:"Futures Trading",type:"FUTURES",icon:"₿",iconColor:"bg-orange-500",balance:A.toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2}),totalPnL:S>=0?"+".concat(S.toFixed(2)):S.toFixed(2),dailyPnL:S>=0?"+".concat((.6*S).toFixed(2)):(.6*S).toFixed(2),change:"".concat(S>=0?"+":"").concat(F.toFixed(2),"%"),changeType:L,positions:C,margin:T.toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2}),isActive:!0},{name:"Margin Trading",type:"MARGIN (Coming Soon)",icon:"Ξ",iconColor:"bg-gray-400",balance:"0.00",totalPnL:"—",dailyPnL:"—",change:"—",changeType:"neutral",positions:0,margin:"0.00",isActive:!1},{name:"Options Trading",type:"OPTIONS (Coming Soon)",icon:"\xd8",iconColor:"bg-gray-400",balance:"0.00",totalPnL:"—",dailyPnL:"—",change:"—",changeType:"neutral",positions:0,margin:"0.00",isActive:!1}],U=P.filter(e=>e.isActive),D=U.reduce((e,t)=>e+parseFloat(t.balance.replace(/,/g,"")),0),R=U.reduce((e,t)=>"—"===t.totalPnL?e:e+parseFloat(t.totalPnL.replace(/[+,]/g,"")),0);return(0,r.jsxs)("div",{className:"bg-card rounded-lg border border-border p-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("h2",{className:"text-lg font-bold text-foreground",children:"Paper Trading Balance"}),i&&"free"!==i.subscription.type&&(0,r.jsxs)(p.E,{variant:"secondary",className:"flex items-center gap-1",children:[(0,r.jsx)(c.A,{className:"h-3 w-3"}),i.subscription.type.charAt(0).toUpperCase()+i.subscription.type.slice(1)]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{onClick:()=>s(!t),className:"p-1 rounded-md hover:bg-muted","aria-label":t?"Hide balance":"Show balance",children:t?(0,r.jsx)(m.A,{className:"h-5 w-5 text-muted-foreground"}):(0,r.jsx)(x.A,{className:"h-5 w-5 text-muted-foreground"})}),(0,r.jsx)("button",{className:"p-1 rounded-md hover:bg-muted",children:(0,r.jsx)(u.A,{className:"h-5 w-5 text-muted-foreground"})})]})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("div",{className:"text-sm text-muted-foreground",children:"Total Virtual Balance"}),i&&(0,r.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Limit: $",i.balance.maximum.toLocaleString()]})]}),(0,r.jsx)("div",{className:"text-3xl font-bold text-foreground",children:t?"$".concat(D.toLocaleString()):"••••••••"}),(0,r.jsxs)("div",{className:"flex items-center mt-1",children:[R>=0?(0,r.jsx)(f.A,{className:"h-4 w-4 text-emerald-500 mr-1"}):(0,r.jsx)(g.A,{className:"h-4 w-4 text-red-500 mr-1"}),(0,r.jsxs)("span",{className:"text-sm font-medium ".concat(R>=0?"text-emerald-500":"text-red-500"),children:[R>=0?"+":"","$",R.toFixed(2)," (",D>0?(R/D*100).toFixed(2):"0.00","%)"]}),(0,r.jsx)("span",{className:"text-xs text-muted-foreground ml-2",children:"Total P&L"})]}),i&&i.balance.current>.8*i.balance.maximum&&(0,r.jsxs)("div",{className:"mt-2 text-xs text-amber-600 dark:text-amber-400",children:["⚠️ Approaching balance limit (",(i.balance.current/i.balance.maximum*100).toFixed(1),"% used)"]})]}),(0,r.jsxs)("div",{className:"flex justify-between mb-4",children:[(0,r.jsx)("h3",{className:"font-medium text-foreground",children:"Trading Accounts"}),(0,r.jsx)("div",{className:"flex space-x-1",children:["24H","1W","1M","1Y","ALL"].map(e=>(0,r.jsx)("button",{className:"px-2 py-1 text-xs rounded ".concat(n===e?"bg-muted text-foreground":"hover:bg-muted text-muted-foreground"),onClick:()=>d(e),children:e},e))})]}),(0,r.jsx)("div",{className:"overflow-x-auto -mx-4 px-4",children:(0,r.jsxs)("table",{className:"min-w-full",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{className:"text-xs text-muted-foreground border-b border-border",children:[(0,r.jsx)("th",{className:"pb-2 text-left",children:"ACCOUNT"}),(0,r.jsx)("th",{className:"pb-2 text-right",children:"BALANCE"}),(0,r.jsx)("th",{className:"pb-2 text-right",children:"P&L"}),(0,r.jsx)("th",{className:"pb-2 text-right",children:"POSITIONS"}),(0,r.jsx)("th",{className:"pb-2 text-right",children:"MARGIN"})]})}),(0,r.jsx)("tbody",{children:P.map((e,s)=>(0,r.jsxs)("tr",{className:"border-b border-border last:border-0 ".concat(e.isActive?"":"opacity-50"),children:[(0,r.jsx)("td",{className:"py-3",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"".concat(e.iconColor," rounded-full h-8 w-8 flex items-center justify-center mr-3"),children:(0,r.jsx)("span",{className:"text-white text-xs",children:e.icon})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium text-foreground",children:e.name}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:e.type})]})]})}),(0,r.jsxs)("td",{className:"py-3 text-right",children:[(0,r.jsx)("div",{className:"font-medium text-foreground",children:e.isActive?t?"$".concat(e.balance):"•••••":"—"}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:"USD"})]}),(0,r.jsxs)("td",{className:"py-3 text-right",children:[(0,r.jsx)("div",{className:"font-medium ".concat(e.isActive?"up"===e.changeType?"text-emerald-500":"down"===e.changeType?"text-red-500":"text-muted-foreground":"text-muted-foreground"),children:e.isActive?t?e.totalPnL:"•••••":"—"}),(0,r.jsx)("div",{className:"text-xs ".concat(e.isActive?"up"===e.changeType?"text-emerald-500":"down"===e.changeType?"text-red-500":"text-muted-foreground":"text-muted-foreground"),children:e.change})]}),(0,r.jsxs)("td",{className:"py-3 text-right",children:[(0,r.jsx)("div",{className:"font-medium text-foreground",children:e.isActive?e.positions:"—"}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:"open"})]}),(0,r.jsxs)("td",{className:"py-3 text-right",children:[(0,r.jsx)("div",{className:"font-medium text-foreground",children:e.isActive?t?"$".concat(e.margin):"•••••":"—"}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:"used"})]})]},s))})]})}),(0,r.jsx)("div",{className:"mt-4 pt-4 border-t border-border",children:(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:"Available"}),(0,r.jsx)("div",{className:"font-medium text-emerald-600",children:t?"$".concat(k.toLocaleString()):"•••••"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:"Margin Used"}),(0,r.jsx)("div",{className:"font-medium text-primary",children:t?"$".concat(T.toLocaleString()):"•••••"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:"Total Positions"}),(0,r.jsx)("div",{className:"font-medium text-foreground",children:C})]})]})})]})}var N=s(381),v=s(85339),y=s(45584),w=s(54416),A=s(53999);let S=y.bL;y.l9;let T=y.ZL;y.bm;let k=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(y.hJ,{ref:t,className:(0,A.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...a})});k.displayName=y.hJ.displayName;let C=a.forwardRef((e,t)=>{let{className:s,children:a,...n}=e;return(0,r.jsxs)(T,{children:[(0,r.jsx)(k,{}),(0,r.jsxs)(y.UC,{ref:t,className:(0,A.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",s),...n,children:[a,(0,r.jsxs)(y.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,r.jsx)(w.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});C.displayName=y.UC.displayName;let F=e=>{let{className:t,...s}=e;return(0,r.jsx)("div",{className:(0,A.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...s})};F.displayName="DialogHeader";let L=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(y.hE,{ref:t,className:(0,A.cn)("text-lg font-semibold leading-none tracking-tight",s),...a})});L.displayName=y.hE.displayName,a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(y.VY,{ref:t,className:(0,A.cn)("text-sm text-muted-foreground",s),...a})}).displayName=y.VY.displayName;var P=s(99708),U=s(74466);let D=(0,U.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),R=a.forwardRef((e,t)=>{let{className:s,variant:a,size:n,asChild:d=!1,...i}=e,l=d?P.DX:"button";return(0,r.jsx)(l,{className:(0,A.cn)(D({variant:a,size:n,className:s})),ref:t,...i})});R.displayName="Button";let $=a.forwardRef((e,t)=>{let{className:s,type:a,...n}=e;return(0,r.jsx)("input",{type:a,className:(0,A.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),ref:t,...n})});$.displayName="Input";var B=s(40968);let E=(0,U.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),O=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(B.b,{ref:t,className:(0,A.cn)(E(),s),...a})});O.displayName=B.b.displayName;let M=(0,U.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),z=a.forwardRef((e,t)=>{let{className:s,variant:a,...n}=e;return(0,r.jsx)("div",{ref:t,role:"alert",className:(0,A.cn)(M({variant:a}),s),...n})});z.displayName="Alert",a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("h5",{ref:t,className:(0,A.cn)("mb-1 font-medium leading-none tracking-tight",s),...a})}).displayName="AlertTitle";let I=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,A.cn)("text-sm [&_p]:leading-relaxed",s),...a})});I.displayName="AlertDescription";var V=s(87489);let W=a.forwardRef((e,t)=>{let{className:s,orientation:a="horizontal",decorative:n=!0,...d}=e;return(0,r.jsx)(V.b,{ref:t,decorative:n,orientation:a,className:(0,A.cn)("shrink-0 bg-border","horizontal"===a?"h-[1px] w-full":"h-full w-[1px]",s),...d})});W.displayName=V.b.displayName;var _=s(71539),Y=s(56671),H=s(60199),q=s(51873);function X(e){let{isOpen:t,onClose:s,currentBalance:n,maxBalance:d,subscriptionType:i}=e,[o,m]=(0,a.useState)(""),[x,u]=(0,a.useState)(!1),[f,g]=(0,a.useState)(!1),h=(0,q.E$)(),j=d-n,N=parseFloat(o)||0,y=async()=>{if(!o||N<=0){Y.oR.error("Please enter a valid amount");return}u(!0);try{let e=await b.A.addFunds({amount:N,method:"virtual"});e.success?(Y.oR.success(e.message,{duration:3e3,action:{label:"View Balance",onClick:()=>console.log("View balance clicked")}}),h.success(e.message,{title:"Funds Added Successfully",duration:4e3,position:"top-right"}),m(""),s()):e.requires_subscription?(g(!0),h.warning("Balance limit reached",{title:"Upgrade Required",duration:5e3})):(Y.oR.error(e.message),h.error(e.message,{title:"Failed to Add Funds",duration:5e3}))}catch(e){Y.oR.error("Failed to add funds")}finally{u(!1)}},w=async e=>{u(!0);try{if(await b.A.upgradeSubscription(e)){let t="Successfully upgraded to ".concat(e," plan!");Y.oR.success(t,{duration:4e3,action:{label:"View Features",onClick:()=>console.log("View features clicked")}}),h.success(t,{title:"Subscription Upgraded",duration:6e3,position:"top-right"}),g(!1);let r=await b.A.addFunds({amount:N,method:"subscription_upgrade"});r.success&&(Y.oR.success(r.message),h.success(r.message,{title:"Funds Added",duration:4e3}),m(""),s())}else Y.oR.error("Failed to upgrade subscription"),h.error("Failed to upgrade subscription",{title:"Upgrade Failed",duration:5e3})}catch(e){Y.oR.error("Failed to upgrade subscription")}finally{u(!1)}};return f?(0,r.jsx)(S,{open:t,onOpenChange:s,children:(0,r.jsxs)(C,{className:"sm:max-w-md",children:[(0,r.jsx)(F,{children:(0,r.jsxs)(L,{className:"flex items-center gap-2",children:[(0,r.jsx)(c.A,{className:"h-5 w-5 text-yellow-500"}),"Upgrade Required"]})}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)(z,{children:[(0,r.jsx)(v.A,{className:"h-4 w-4"}),(0,r.jsxs)(I,{children:["You've reached your current balance limit of ",d.toLocaleString()," USDT. Upgrade your subscription to add more funds."]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"border rounded-lg p-4 space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(c.A,{className:"h-4 w-4 text-yellow-500"}),(0,r.jsx)("span",{className:"font-medium",children:"Premium Plan"})]}),(0,r.jsxs)(p.E,{variant:"secondary",children:["$",H.ND.premium.price,"/month"]})]}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Up to ",H.ND.premium.maxBalance.toLocaleString()," USDT balance"]}),(0,r.jsx)(R,{onClick:()=>w("premium"),disabled:x,className:"w-full",children:"Upgrade to Premium"})]}),(0,r.jsxs)("div",{className:"border rounded-lg p-4 space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(_.A,{className:"h-4 w-4 text-purple-500"}),(0,r.jsx)("span",{className:"font-medium",children:"Pro Plan"})]}),(0,r.jsxs)(p.E,{variant:"secondary",children:["$",H.ND.pro.price,"/month"]})]}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Up to ",H.ND.pro.maxBalance.toLocaleString()," USDT balance + API access"]}),(0,r.jsx)(R,{onClick:()=>w("pro"),disabled:x,variant:"outline",className:"w-full",children:"Upgrade to Pro"})]})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(R,{variant:"outline",onClick:()=>g(!1),className:"flex-1",children:"Back"}),(0,r.jsx)(R,{variant:"outline",onClick:s,className:"flex-1",children:"Cancel"})]})]})]})}):(0,r.jsx)(S,{open:t,onOpenChange:s,children:(0,r.jsxs)(C,{className:"sm:max-w-md",children:[(0,r.jsx)(F,{children:(0,r.jsxs)(L,{className:"flex items-center gap-2",children:[(0,r.jsx)(l.A,{className:"h-5 w-5"}),"Add Virtual Funds"]})}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"bg-muted/50 rounded-lg p-4 space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{children:"Current Balance:"}),(0,r.jsxs)("span",{className:"font-medium",children:[n.toLocaleString()," USDT"]})]}),(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{children:"Available to Add:"}),(0,r.jsxs)("span",{className:"font-medium text-green-600",children:[j.toLocaleString()," USDT"]})]}),(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{children:"Plan Limit:"}),(0,r.jsxs)("span",{className:"font-medium",children:[d.toLocaleString()," USDT"]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(O,{htmlFor:"amount",children:"Amount (USDT)"}),(0,r.jsx)($,{id:"amount",type:"number",placeholder:"Enter amount",value:o,onChange:e=>m(e.target.value),min:"0",max:j})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(O,{children:"Quick Add"}),(0,r.jsx)("div",{className:"grid grid-cols-2 gap-2",children:[1e3,5e3,1e4,25e3].map(e=>(0,r.jsx)(R,{variant:"outline",size:"sm",onClick:()=>m(e.toString()),disabled:e>j,children:e.toLocaleString()},e))})]}),N>j&&(0,r.jsxs)(z,{children:[(0,r.jsx)(v.A,{className:"h-4 w-4"}),(0,r.jsxs)(I,{children:["Amount exceeds available funds. Maximum you can add: ",j.toLocaleString()," USDT"]})]}),(0,r.jsx)(W,{}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(R,{variant:"outline",onClick:s,className:"flex-1",children:"Cancel"}),(0,r.jsx)(R,{onClick:y,disabled:x||!o||N<=0||N>j,className:"flex-1",children:x?"Adding...":"Add Funds"})]}),"free"===i&&(0,r.jsx)("div",{className:"text-center",children:(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Need more than ",d.toLocaleString()," USDT?"," ",(0,r.jsx)("button",{onClick:()=>g(!0),className:"text-primary hover:underline",children:"Upgrade your plan"})]})})]})]})})}function G(){var e;let[t,s]=(0,a.useState)("futures"),[n,d]=(0,a.useState)(null),[c,m]=(0,a.useState)(!1),{accountInfo:x,positions:u,getTotalPnL:f,getTotalMargin:g,getAvailableBalance:p,trades:j}=(0,h.fx)();(0,a.useEffect)(()=>{let e=b.A.subscribe(e=>{d(e)});return d(b.A.getUser()),e},[]);let y=(null==n?void 0:null===(e=n.balance)||void 0===e?void 0:e.current)||1e4,w=(null==x?void 0:x.totalWalletBalance)||y,A=f()||0,S=g()||0,T=p();(null==T||0===T)&&(T=Math.max(0,w-S+A));let k=u.length,C=j.length,F=j.filter(e=>e.realizedPnl>0).length,L=C>0?F/C*100:0,P=j.length>0?Math.max(...j.map(e=>e.realizedPnl)):0,U=j.length>0?Math.min(...j.map(e=>e.realizedPnl)):0,D={futures:{name:"Futures Trading",balance:w,available:T,margin:S,pnl:A,positions:k,color:"orange"},margin:{name:"Margin Trading",balance:0,available:0,margin:0,pnl:0,positions:0,color:"blue"},options:{name:"Options Trading",balance:0,available:0,margin:0,pnl:0,positions:0,color:"purple"}}[t];return(0,r.jsxs)("div",{className:"bg-card rounded-lg border border-border p-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsx)("h2",{className:"text-lg font-bold text-foreground",children:"Account Manager"}),(0,r.jsx)("button",{className:"p-1 rounded-md hover:bg-muted",children:(0,r.jsx)(N.A,{className:"h-5 w-5 text-muted-foreground"})})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-foreground mb-2",children:"Select Account"}),(0,r.jsxs)("select",{value:t,onChange:e=>s(e.target.value),className:"w-full border border-border rounded-md px-3 py-2 bg-card text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary",children:[(0,r.jsx)("option",{value:"futures",children:"Futures Trading"}),(0,r.jsx)("option",{value:"margin",disabled:!0,children:"Margin Trading (Coming Soon)"}),(0,r.jsx)("option",{value:"options",disabled:!0,children:"Options Trading (Coming Soon)"})]})]}),"futures"===t?(0,r.jsxs)("div",{className:"bg-muted rounded-lg p-4 mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsx)("h3",{className:"font-medium text-foreground",children:D.name}),(0,r.jsxs)("div",{className:"px-2 py-1 rounded text-xs font-medium ".concat(D.pnl>=0?"bg-emerald-100 text-emerald-700 dark:bg-emerald-900/20 dark:text-emerald-400":"bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400"),children:[D.pnl>=0?"+":"","$",D.pnl.toFixed(2)]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-3 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-muted-foreground",children:"Balance"}),(0,r.jsxs)("div",{className:"font-medium text-foreground",children:["$",D.balance.toLocaleString()]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-muted-foreground",children:"Available"}),(0,r.jsxs)("div",{className:"font-medium text-emerald-600 dark:text-emerald-400",children:["$",D.available.toLocaleString()]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-muted-foreground",children:"Margin Used"}),(0,r.jsxs)("div",{className:"font-medium text-primary",children:["$",D.margin.toLocaleString()]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-muted-foreground",children:"Positions"}),(0,r.jsx)("div",{className:"font-medium text-foreground",children:D.positions})]})]}),(0,r.jsxs)("div",{className:"mt-3",children:[(0,r.jsxs)("div",{className:"flex justify-between text-xs text-muted-foreground mb-1",children:[(0,r.jsx)("span",{children:"Margin Usage"}),(0,r.jsxs)("span",{children:[(D.margin/D.balance*100).toFixed(1),"%"]})]}),(0,r.jsx)("div",{className:"w-full bg-muted rounded-full h-2",children:(0,r.jsx)("div",{className:"h-2 rounded-full ".concat(D.margin/D.balance>.8?"bg-red-500":D.margin/D.balance>.6?"bg-yellow-500":"bg-emerald-500"),style:{width:"".concat(D.margin/D.balance*100,"%")}})})]})]}):(0,r.jsx)("div",{className:"bg-muted rounded-lg p-4 mb-4",children:(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("div",{className:"text-muted-foreground mb-2",children:"\uD83D\uDEA7"}),(0,r.jsx)("h3",{className:"font-medium text-foreground mb-2",children:"margin"===t?"Margin Trading":"Options Trading"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"Coming Soon"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"This trading type is currently under development and will be available in a future update."})]})}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("button",{onClick:()=>m(!0),className:"w-full py-2 px-4 rounded-md text-sm font-medium flex items-center justify-center ".concat("futures"===t?"bg-primary text-primary-foreground hover:bg-primary/90":"bg-muted text-muted-foreground cursor-not-allowed"),disabled:"futures"!==t,children:[(0,r.jsx)(l.A,{className:"h-4 w-4 mr-2"}),"Add Funds"]}),(0,r.jsxs)("button",{className:"w-full py-2 px-4 rounded-md text-sm font-medium flex items-center justify-center ".concat("futures"===t?"bg-card border border-border text-foreground hover:bg-muted":"bg-muted border border-border text-muted-foreground cursor-not-allowed"),disabled:"futures"!==t,children:[(0,r.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Transfer"]}),(0,r.jsxs)("button",{onClick:()=>{"futures"===t&&confirm("Are you sure you want to reset your account? This will restore your balance to $10,000 and clear all trading history.")&&b.A.resetUser()},className:"w-full py-2 px-4 rounded-md text-sm font-medium flex items-center justify-center ".concat("futures"===t?"bg-card border border-border text-foreground hover:bg-muted":"bg-muted border border-border text-muted-foreground cursor-not-allowed"),disabled:"futures"!==t,children:[(0,r.jsx)(i.A,{className:"h-4 w-4 mr-2"}),"Reset Account"]})]}),(0,r.jsx)("div",{className:"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md dark:bg-yellow-900/20 dark:border-yellow-800",children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(v.A,{className:"h-4 w-4 text-yellow-600 dark:text-yellow-400 mt-0.5 mr-2 flex-shrink-0"}),(0,r.jsxs)("div",{className:"text-xs text-yellow-800 dark:text-yellow-200",children:[(0,r.jsx)("p",{className:"font-medium mb-1",children:"Paper Trading Notice"}),(0,r.jsx)("p",{children:"This is virtual money for educational purposes. No real funds are at risk."})]})]})}),"futures"===t&&(0,r.jsxs)("div",{className:"mt-4 pt-4 border-t border-border",children:[(0,r.jsx)("h4",{className:"text-sm font-medium mb-2 text-foreground",children:"Performance Summary"}),(0,r.jsxs)("div",{className:"space-y-2 text-xs",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Win Rate"}),(0,r.jsxs)("span",{className:"font-medium text-foreground",children:[L.toFixed(1),"%"]})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Total Trades"}),(0,r.jsx)("span",{className:"font-medium text-foreground",children:C})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Best Trade"}),(0,r.jsxs)("span",{className:"font-medium text-emerald-600 dark:text-emerald-400",children:[P>=0?"+":"","$",P.toFixed(2)]})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Worst Trade"}),(0,r.jsxs)("span",{className:"font-medium text-red-600 dark:text-red-400",children:[U>=0?"+":"","$",U.toFixed(2)]})]})]})]}),n&&(0,r.jsx)(X,{isOpen:c,onClose:()=>m(!1),currentBalance:n.balance.current,maxBalance:n.balance.maximum,subscriptionType:n.subscription.type})]})}var J=s(1482),Q=s(47924);function Z(){let[e,t]=(0,a.useState)("all"),[s,n]=(0,a.useState)(""),{trades:i,accountInfo:l}=(0,h.fx)(),o=[{id:"TXN_INIT",type:"deposit",account:"Futures Trading",description:"Initial virtual deposit",amount:"+$".concat(((null==l?void 0:l.totalWalletBalance)||5e4).toLocaleString(),".00"),balance:"$".concat(((null==l?void 0:l.totalWalletBalance)||5e4).toLocaleString(),".00"),timestamp:new Date(Date.now()-6048e5).toISOString(),status:"completed"},...i.map((e,t)=>({id:e.id,type:0===e.realizedPnl?"trade":"pnl",account:"Futures Trading",description:0===e.realizedPnl?"".concat(e.symbol," ").concat(e.side," Position ").concat("BUY"===e.side?"Opened":"Closed"):"".concat(e.symbol," Position P&L"),amount:0===e.realizedPnl?"-$".concat((e.quantity*e.price).toLocaleString()):"".concat(e.realizedPnl>=0?"+":"","$").concat(e.realizedPnl.toFixed(2)),balance:"$".concat(((null==l?void 0:l.totalWalletBalance)||5e4).toLocaleString(),".00"),timestamp:new Date(e.timestamp).toISOString(),status:"completed"}))].sort((e,t)=>new Date(t.timestamp).getTime()-new Date(e.timestamp).getTime()),c=e=>{switch(e){case"deposit":return"↓";case"trade":return"⚡";case"pnl":return"\uD83D\uDCC8";case"transfer":return"↔";default:return"•"}},m=e=>{switch(e){case"deposit":return"bg-emerald-100 text-emerald-700";case"trade":return"bg-blue-100 text-blue-700";case"pnl":return"bg-purple-100 text-purple-700";case"transfer":return"bg-orange-100 text-orange-700";default:return"bg-gray-100 text-gray-700"}},x=o.filter(t=>("all"===e||t.type===e)&&(!s||!!t.description.toLowerCase().includes(s.toLowerCase())));return(0,r.jsxs)("div",{className:"bg-card rounded-lg border border-border p-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsx)("h2",{className:"text-lg font-bold text-foreground",children:"Transaction History"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{className:"p-2 rounded-md hover:bg-muted",children:(0,r.jsx)(d.A,{className:"h-4 w-4 text-muted-foreground"})}),(0,r.jsx)("button",{className:"p-2 rounded-md hover:bg-muted",children:(0,r.jsx)(J.A,{className:"h-4 w-4 text-muted-foreground"})})]})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 mb-4",children:[(0,r.jsxs)("div",{className:"flex-1 relative",children:[(0,r.jsx)(Q.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,r.jsx)("input",{type:"text",placeholder:"Search transactions...",value:s,onChange:e=>n(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-border rounded-md bg-card text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"})]}),(0,r.jsx)("div",{className:"flex space-x-1",children:["all","deposit","trade","pnl","transfer"].map(s=>(0,r.jsx)("button",{className:"px-3 py-2 text-xs font-medium rounded-md capitalize ".concat(e===s?"bg-primary text-primary-foreground":"bg-muted text-foreground hover:bg-muted/80"),onClick:()=>t(s),children:s},s))})]}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{className:"text-xs text-muted-foreground border-b border-border",children:[(0,r.jsx)("th",{className:"pb-2 text-left",children:"TYPE"}),(0,r.jsx)("th",{className:"pb-2 text-left",children:"DESCRIPTION"}),(0,r.jsx)("th",{className:"pb-2 text-left",children:"ACCOUNT"}),(0,r.jsx)("th",{className:"pb-2 text-right",children:"AMOUNT"}),(0,r.jsx)("th",{className:"pb-2 text-right",children:"BALANCE"}),(0,r.jsx)("th",{className:"pb-2 text-right",children:"TIME"})]})}),(0,r.jsx)("tbody",{children:x.map(e=>(0,r.jsxs)("tr",{className:"border-b border-border last:border-0 hover:bg-muted",children:[(0,r.jsx)("td",{className:"py-3",children:(0,r.jsx)("div",{className:"inline-flex items-center justify-center w-6 h-6 rounded-full text-xs font-medium ".concat(m(e.type)),children:c(e.type)})}),(0,r.jsxs)("td",{className:"py-3",children:[(0,r.jsx)("div",{className:"font-medium text-sm text-foreground",children:e.description}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:e.id})]}),(0,r.jsx)("td",{className:"py-3",children:(0,r.jsx)("div",{className:"text-sm text-foreground",children:e.account})}),(0,r.jsx)("td",{className:"py-3 text-right",children:(0,r.jsx)("div",{className:"font-medium ".concat(e.amount.startsWith("+")?"text-emerald-600 dark:text-emerald-400":"text-red-600 dark:text-red-400"),children:e.amount})}),(0,r.jsx)("td",{className:"py-3 text-right",children:(0,r.jsx)("div",{className:"font-medium text-foreground",children:e.balance})}),(0,r.jsxs)("td",{className:"py-3 text-right",children:[(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:new Date(e.timestamp).toLocaleDateString()}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:new Date(e.timestamp).toLocaleTimeString()})]})]},e.id))})]})}),0===x.length&&(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("div",{className:"text-muted-foreground mb-2",children:"No transactions found"}),(0,r.jsx)("div",{className:"text-sm text-muted-foreground",children:"Try adjusting your search or filter criteria"})]}),(0,r.jsx)("div",{className:"mt-4 pt-4 border-t border-border",children:(0,r.jsxs)("div",{className:"grid grid-cols-2 sm:grid-cols-4 gap-4 text-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:"Total Deposits"}),(0,r.jsxs)("div",{className:"font-medium text-emerald-600 dark:text-emerald-400",children:["$",((null==l?void 0:l.totalWalletBalance)||5e4).toLocaleString(),".00"]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:"Total Trades"}),(0,r.jsx)("div",{className:"font-medium text-primary",children:i.length})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:"Net P&L"}),(0,r.jsxs)("div",{className:"font-medium ".concat(i.reduce((e,t)=>e+t.realizedPnl,0)>=0?"text-emerald-600 dark:text-emerald-400":"text-red-600 dark:text-red-400"),children:[i.reduce((e,t)=>e+t.realizedPnl,0)>=0?"+":"","$",i.reduce((e,t)=>e+t.realizedPnl,0).toFixed(2)]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:"Transactions"}),(0,r.jsx)("div",{className:"font-medium text-foreground",children:o.length})]})]})})]})}function K(){let[e,t]=(0,a.useState)("overview"),[s,c]=(0,a.useState)(null),[m,x]=(0,a.useState)(!1);return(0,q.E$)(),(0,a.useEffect)(()=>{let e=b.A.subscribe(e=>{c(e)});return c(b.A.getUser()),e},[]),(0,r.jsxs)("div",{className:"p-3 sm:p-4",children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center justify-between mb-4 sm:mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center mb-3 md:mb-0",children:[(0,r.jsx)("div",{className:"bg-primary/10 rounded-full p-2 mr-3",children:(0,r.jsx)(n.A,{className:"h-5 w-5 sm:h-6 sm:w-6 text-primary"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-xl sm:text-2xl font-bold text-foreground",children:"Paper Trading Wallet"}),(0,r.jsx)("p",{className:"text-muted-foreground text-xs sm:text-sm",children:"Manage your virtual trading funds"})]})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2 sm:space-x-2 sm:gap-0",children:[(0,r.jsxs)("button",{className:"px-3 sm:px-4 py-2 bg-card border border-border rounded-md text-xs sm:text-sm font-medium flex items-center justify-center hover:bg-muted text-foreground",children:[(0,r.jsx)(d.A,{className:"h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2"}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"Export Report"}),(0,r.jsx)("span",{className:"sm:hidden",children:"Export"})]}),(0,r.jsxs)("button",{className:"px-3 sm:px-4 py-2 bg-primary text-primary-foreground rounded-md text-xs sm:text-sm font-medium hover:bg-primary/90 flex items-center justify-center",children:[(0,r.jsx)(i.A,{className:"h-3 w-3 sm:h-4 sm:w-4 inline-block mr-1 sm:mr-2"}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"Reset Balances"}),(0,r.jsx)("span",{className:"sm:hidden",children:"Reset"})]})]})]}),(0,r.jsx)("div",{className:"mb-4 sm:mb-6",children:(0,r.jsxs)("div",{className:"flex overflow-x-auto pb-2 -mx-1 gap-1",children:[(0,r.jsx)("button",{className:"px-3 sm:px-4 py-2 mx-1 rounded-md text-xs sm:text-sm font-medium whitespace-nowrap ".concat("overview"===e?"bg-primary text-primary-foreground":"bg-card border border-border text-foreground hover:bg-muted"),onClick:()=>t("overview"),children:"Overview"}),(0,r.jsxs)("button",{className:"px-3 sm:px-4 py-2 mx-1 rounded-md text-xs sm:text-sm font-medium whitespace-nowrap flex items-center ".concat("add-funds"===e?"bg-primary text-primary-foreground":"bg-card border border-border text-foreground hover:bg-muted"),onClick:()=>t("add-funds"),children:[(0,r.jsx)(l.A,{className:"h-3 w-3 sm:h-4 sm:w-4 mr-1"}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"Add Funds"}),(0,r.jsx)("span",{className:"sm:hidden",children:"Add"})]}),(0,r.jsxs)("button",{className:"px-3 sm:px-4 py-2 mx-1 rounded-md text-xs sm:text-sm font-medium whitespace-nowrap flex items-center ".concat("transfer"===e?"bg-primary text-primary-foreground":"bg-card border border-border text-foreground hover:bg-muted"),onClick:()=>t("transfer"),children:[(0,r.jsx)(o.A,{className:"h-3 w-3 sm:h-4 sm:w-4 mr-1"}),"Transfer"]}),(0,r.jsx)("button",{className:"px-3 sm:px-4 py-2 mx-1 rounded-md text-xs sm:text-sm font-medium whitespace-nowrap ".concat("history"===e?"bg-primary text-primary-foreground":"bg-card border border-border text-foreground hover:bg-muted"),onClick:()=>t("history"),children:"History"})]})}),"overview"===e&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 mb-4 sm:mb-6",children:[(0,r.jsx)("div",{className:"lg:col-span-2",children:(0,r.jsx)(j,{})}),(0,r.jsx)("div",{children:(0,r.jsx)(G,{})})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 gap-4 sm:gap-6",children:(0,r.jsx)(Z,{})})]}),"add-funds"===e&&(0,r.jsxs)("div",{className:"bg-card rounded-lg border border-border p-4 sm:p-6",children:[(0,r.jsx)("h2",{className:"text-base sm:text-lg font-bold mb-3 sm:mb-4 text-foreground",children:"Add Virtual Funds"}),(0,r.jsx)("p",{className:"text-muted-foreground mb-4 sm:mb-6 text-sm sm:text-base",children:"Add virtual funds to your paper trading accounts"}),s&&(0,r.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,r.jsxs)("div",{className:"bg-muted rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-foreground",children:"Current Balance:"}),(0,r.jsxs)("span",{className:"text-lg font-bold text-foreground",children:["$",s.balance.current.toLocaleString()]})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-foreground",children:"Maximum Balance:"}),(0,r.jsxs)("span",{className:"text-sm text-foreground",children:["$",s.balance.maximum.toLocaleString()]})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-foreground",children:"Available to Add:"}),(0,r.jsxs)("span",{className:"text-sm text-green-600 dark:text-green-400",children:["$",(s.balance.maximum-s.balance.current).toLocaleString()]})]})]}),(0,r.jsx)("div",{className:"bg-primary/10 border border-primary/20 rounded-md p-4",children:(0,r.jsxs)("div",{className:"text-sm text-primary",children:[(0,r.jsx)("p",{className:"font-medium mb-2",children:"Paper Trading Info:"}),(0,r.jsxs)("ul",{className:"list-disc pl-5 space-y-1 text-primary/80",children:[(0,r.jsx)("li",{children:"Virtual funds are for simulation purposes only"}),(0,r.jsx)("li",{children:"No real money is involved in paper trading"}),(0,r.jsx)("li",{children:"Perfect for learning and strategy testing"}),(0,r.jsx)("li",{children:"Reset balances anytime from the account manager"}),"free"===s.subscription.type&&(0,r.jsx)("li",{children:"Upgrade your plan to increase balance limits"})]})]})}),(0,r.jsx)("button",{onClick:()=>x(!0),className:"w-full bg-primary text-primary-foreground py-2 rounded-md font-medium hover:bg-primary/90",disabled:s.balance.current>=s.balance.maximum,children:s.balance.current>=s.balance.maximum?"Balance Limit Reached":"Add Virtual Funds"})]})]}),"transfer"===e&&(0,r.jsxs)("div",{className:"bg-card rounded-lg border border-border p-4 sm:p-6",children:[(0,r.jsx)("h2",{className:"text-base sm:text-lg font-bold mb-3 sm:mb-4 text-foreground",children:"Transfer Between Accounts"}),(0,r.jsx)("p",{className:"text-muted-foreground mb-4 sm:mb-6 text-sm sm:text-base",children:"Transfer virtual funds between your paper trading accounts"}),(0,r.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-foreground mb-1",children:"From Account"}),(0,r.jsxs)("select",{className:"w-full border border-border rounded-md px-3 py-2 bg-card text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary",children:[(0,r.jsx)("option",{children:"Futures Trading ($50,000.00)"}),(0,r.jsx)("option",{disabled:!0,children:"Margin Trading (Coming Soon)"}),(0,r.jsx)("option",{disabled:!0,children:"Options Trading (Coming Soon)"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-foreground mb-1",children:"To Account"}),(0,r.jsxs)("select",{className:"w-full border border-border rounded-md px-3 py-2 bg-card text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary",children:[(0,r.jsx)("option",{disabled:!0,children:"Margin Trading (Coming Soon)"}),(0,r.jsx)("option",{disabled:!0,children:"Futures Trading (Same Account)"}),(0,r.jsx)("option",{disabled:!0,children:"Options Trading (Coming Soon)"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-foreground mb-1",children:"Amount (USD)"}),(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("input",{type:"number",placeholder:"1000.00",className:"w-full border border-border rounded-l-md px-3 py-2 bg-card text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"}),(0,r.jsx)("button",{className:"bg-muted text-foreground px-4 rounded-r-md border-t border-r border-b border-border hover:bg-muted/80",children:"MAX"})]}),(0,r.jsxs)("div",{className:"flex justify-between mt-1",children:[(0,r.jsx)("span",{className:"text-xs text-muted-foreground",children:"Available: $50,000.00"}),(0,r.jsx)("span",{className:"text-xs text-muted-foreground",children:"No transfer fees for paper trading"})]})]})]}),(0,r.jsxs)("div",{className:"bg-primary/10 border border-primary/20 rounded-md p-4 mb-6",children:[(0,r.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,r.jsx)("span",{className:"text-sm text-primary",children:"Transfer Amount"}),(0,r.jsx)("span",{className:"text-sm font-medium text-primary",children:"$1,000.00"})]}),(0,r.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,r.jsx)("span",{className:"text-sm text-primary",children:"Transfer Fee"}),(0,r.jsx)("span",{className:"text-sm font-medium text-primary",children:"$0.00"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-primary",children:"You Will Receive"}),(0,r.jsx)("span",{className:"text-sm font-medium text-primary",children:"$1,000.00"})]})]}),(0,r.jsx)("button",{className:"w-full bg-primary text-primary-foreground py-2 rounded-md font-medium hover:bg-primary/90",children:"Transfer Funds"})]}),"history"===e&&(0,r.jsxs)("div",{className:"bg-card rounded-lg border border-border p-4 sm:p-6",children:[(0,r.jsx)("h2",{className:"text-base sm:text-lg font-bold mb-3 sm:mb-4 text-foreground",children:"Account History"}),(0,r.jsx)("p",{className:"text-muted-foreground mb-4 sm:mb-6 text-sm sm:text-base",children:"View your paper trading account transactions and balance changes"}),(0,r.jsx)(Z,{})]}),s&&(0,r.jsx)(X,{isOpen:m,onClose:()=>x(!1),currentBalance:s.balance.current,maxBalance:s.balance.maximum,subscriptionType:s.subscription.type})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[992,965,879,55,776,855,344,475,135,441,684,358],()=>t(54493)),_N_E=e.O()}]);