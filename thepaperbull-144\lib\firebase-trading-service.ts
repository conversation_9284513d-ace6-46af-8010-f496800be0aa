// Firebase-based trading service that replaces mock data with real Firestore storage
import type {
  Position,
  Order,
  Trade,
  MarketData,
  TradingState,
  OrderRequest,
  PositionUpdate,
  AccountInfo,
  AccountBalance,
  TradingService as ITradingService
} from '../types/trading'
import { firestoreService, UserPosition, UserOrder, UserTrade } from './firestore-service'
import userService from './user-service'
import notificationService from './notification-service'

class FirebaseTradingService implements ITradingService {
  private state: TradingState = {
    positions: [],
    orders: [],
    trades: [],
    marketData: {},
    accountInfo: null,
    isLoading: false,
    error: null
  }

  private subscribers: ((state: TradingState) => void)[] = []
  private unsubscribePositions: (() => void) | null = null
  private unsubscribeOrders: (() => void) | null = null
  private userUnsubscribe: (() => void) | null = null
  private userServiceUnsubscribe: (() => void) | null = null
  private orderIdCounter = 1
  private positionIdCounter = 1
  private tradeIdCounter = 1

  constructor() {
    this.initializeUserSubscription()
  }

  // Subscribe to state changes
  subscribe(callback: (state: TradingState) => void): () => void {
    this.subscribers.push(callback)

    // Return unsubscribe function
    return () => {
      const index = this.subscribers.indexOf(callback)
      if (index > -1) {
        this.subscribers.splice(index, 1)
      }
    }
  }

  private notifySubscribers() {
    this.subscribers.forEach(callback => callback(this.state))
  }

  private initializeUserSubscription() {
    // Subscribe to user changes to load their trading data
    this.userUnsubscribe = userService.subscribe((user) => {
      if (user) {
        this.loadUserTradingData(user.id)
        this.initializeAccountData()
      } else {
        this.clearTradingData()
      }
    })
  }

  private async loadUserTradingData(userId: string) {
    try {
      this.state.isLoading = true
      this.notifySubscribers()

      // Subscribe to real-time positions with error handling
      if (this.unsubscribePositions) {
        this.unsubscribePositions()
      }

      try {
        this.unsubscribePositions = firestoreService.subscribeToUserPositions(userId, (positions) => {
          this.state.positions = positions.map(this.convertUserPositionToPosition)
          this.updateAccountInfo()
          this.notifySubscribers()
        })
      } catch (positionsError) {
        console.error('Error subscribing to positions:', positionsError)
        // Continue with empty positions array
        this.state.positions = []
      }

      // Subscribe to real-time orders with error handling
      if (this.unsubscribeOrders) {
        this.unsubscribeOrders()
      }

      try {
        this.unsubscribeOrders = firestoreService.subscribeToUserOrders(userId, (orders) => {
          this.state.orders = orders.map(this.convertUserOrderToOrder)
          this.updateAccountInfo()
          this.notifySubscribers()
        })
      } catch (ordersError) {
        console.error('Error subscribing to orders:', ordersError)
        // Continue with empty orders array
        this.state.orders = []
      }

      // Load trade history with error handling
      try {
        const trades = await firestoreService.getUserTrades(userId)
        this.state.trades = trades.map(this.convertUserTradeToTrade)
      } catch (tradesError) {
        console.error('Error loading trades:', tradesError)
        // Continue with empty trades array
        this.state.trades = []
      }

      this.state.isLoading = false
      this.state.error = null
      this.notifySubscribers()
    } catch (error) {
      console.error('Error loading user trading data:', error)
      this.state.isLoading = false

      // Check if it's a permissions error
      if (error.code === 'permission-denied') {
        this.state.error = 'Trading data access requires proper authentication. Please sign in again.'
      } else {
        this.state.error = 'Failed to load trading data. Please try again.'
      }

      // Initialize with empty data so the app can still function
      this.state.positions = []
      this.state.orders = []
      this.state.trades = []

      this.notifySubscribers()
    }
  }

  private clearTradingData() {
    this.state.positions = []
    this.state.orders = []
    this.state.trades = []
    this.state.accountInfo = null

    if (this.unsubscribePositions) {
      this.unsubscribePositions()
      this.unsubscribePositions = null
    }

    if (this.unsubscribeOrders) {
      this.unsubscribeOrders()
      this.unsubscribeOrders = null
    }

    if (this.userServiceUnsubscribe) {
      this.userServiceUnsubscribe()
      this.userServiceUnsubscribe = null
    }

    this.notifySubscribers()
  }

  private convertUserPositionToPosition = (userPosition: UserPosition): Position => {
    return {
      id: userPosition.id,
      symbol: userPosition.symbol,
      side: userPosition.side,
      entryPrice: userPosition.entryPrice,
      markPrice: userPosition.markPrice,
      size: userPosition.size,
      margin: userPosition.margin,
      leverage: userPosition.leverage,
      pnl: userPosition.pnl,
      pnlPercent: userPosition.pnlPercent,
      liquidationPrice: userPosition.liquidationPrice,
      stopLoss: userPosition.stopLoss,
      takeProfit: userPosition.takeProfit,
      timestamp: userPosition.createdAt?.toMillis?.() || Date.now(),
      orderId: userPosition.orderId
    }
  }

  private convertUserOrderToOrder = (userOrder: UserOrder): Order => {
    return {
      id: userOrder.id,
      symbol: userOrder.symbol,
      side: userOrder.side,
      type: userOrder.type,
      price: userOrder.price,
      origQty: userOrder.origQty,
      executedQty: userOrder.executedQty,
      status: userOrder.status,
      leverage: userOrder.leverage,
      stopLoss: userOrder.stopLoss,
      takeProfit: userOrder.takeProfit,
      timestamp: userOrder.createdAt?.toMillis?.() || Date.now()
    }
  }

  private convertUserTradeToTrade = (userTrade: UserTrade): Trade => {
    return {
      id: userTrade.id,
      symbol: userTrade.symbol,
      side: userTrade.side,
      price: userTrade.price,
      quantity: userTrade.quantity,
      commission: userTrade.commission,
      realizedPnl: userTrade.realizedPnl,
      leverage: userTrade.leverage,
      timestamp: userTrade.createdAt?.toMillis?.() || Date.now(),
      orderId: userTrade.orderId,
      positionId: userTrade.positionId
    }
  }

  private initializeAccountData() {
    // Subscribe to user service for real-time balance updates
    this.userServiceUnsubscribe = userService.subscribe((userData) => {
      if (userData) {
        this.syncAccountWithUserData(userData)
      }
    })

    // Initial sync
    const userBalance = userService.getUserBalance() || 10000
    this.syncAccountWithUserData(userService.getUser())
  }

  private syncAccountWithUserData(userData: any) {
    const userBalance = userData?.balance?.current || userService.getUserBalance() || 10000

    const usdtBalance: AccountBalance = {
      asset: 'USDT',
      free: userBalance,
      locked: 0.00,
      total: userBalance
    }

    // Preserve existing calculated values or initialize them
    const existingAccountInfo = this.state.accountInfo
    const totalUnrealizedProfit = existingAccountInfo?.totalUnrealizedProfit || 0.00
    const totalPositionInitialMargin = existingAccountInfo?.totalPositionInitialMargin || 0.00
    const totalOpenOrderInitialMargin = existingAccountInfo?.totalOpenOrderInitialMargin || 0.00

    // Calculate available balance properly: total balance - used margin + unrealized PnL
    const availableBalance = userBalance - totalPositionInitialMargin - totalOpenOrderInitialMargin + totalUnrealizedProfit

    this.state.accountInfo = {
      totalWalletBalance: userBalance,
      totalUnrealizedProfit: totalUnrealizedProfit,
      totalMarginBalance: userBalance + totalUnrealizedProfit,
      totalPositionInitialMargin: totalPositionInitialMargin,
      totalOpenOrderInitialMargin: totalOpenOrderInitialMargin,
      availableBalance: Math.max(0, availableBalance), // Ensure it's never negative
      maxWithdrawAmount: Math.max(0, availableBalance),
      balances: [usdtBalance],
      canTrade: true,
      canDeposit: true,
      canWithdraw: true,
      updateTime: Date.now()
    }

    this.notifySubscribers()
  }

  private updateAccountInfo() {
    if (!this.state.accountInfo) return

    // Calculate total PnL from positions
    const totalPnL = this.state.positions.reduce((sum, pos) => sum + pos.pnl, 0)

    // Calculate total margin used
    const totalMargin = this.state.positions.reduce((sum, pos) => sum + pos.margin, 0)

    // Calculate total order margin (for pending orders)
    const totalOrderMargin = this.state.orders
      .filter(order => order.status === 'NEW')
      .reduce((sum, order) => {
        const orderValue = order.origQty * order.price
        return sum + (orderValue / (order.leverage || 10))
      }, 0)

    // Update account info with proper calculations
    this.state.accountInfo.totalUnrealizedProfit = totalPnL
    this.state.accountInfo.totalPositionInitialMargin = totalMargin
    this.state.accountInfo.totalOpenOrderInitialMargin = totalOrderMargin
    this.state.accountInfo.totalMarginBalance = this.state.accountInfo.totalWalletBalance + totalPnL

    // Available balance = total wallet balance - used margin for positions - used margin for orders + unrealized PnL
    const availableBalance = this.state.accountInfo.totalWalletBalance - totalMargin - totalOrderMargin + totalPnL
    this.state.accountInfo.availableBalance = Math.max(0, availableBalance)
    this.state.accountInfo.maxWithdrawAmount = Math.max(0, availableBalance)
    this.state.accountInfo.updateTime = Date.now()

    // Update the USDT balance in the balances array
    const usdtBalance = this.state.accountInfo.balances.find(b => b.asset === 'USDT')
    if (usdtBalance) {
      usdtBalance.free = this.state.accountInfo.availableBalance
      usdtBalance.locked = totalMargin + totalOrderMargin
      usdtBalance.total = this.state.accountInfo.totalWalletBalance
    }
  }

  // State getters
  getPositions(): Position[] {
    return [...this.state.positions]
  }

  getOrders(): Order[] {
    return [...this.state.orders]
  }

  getTrades(): Trade[] {
    return [...this.state.trades]
  }

  getMarketData(symbol: string): MarketData | null {
    return this.state.marketData[symbol] || null
  }

  getAccountInfo(): AccountInfo | null {
    return this.state.accountInfo
  }

  // Update market data
  updateMarketData(symbol: string, data: MarketData): void {
    this.state.marketData[symbol] = data

    // Update position PnL based on new market data
    this.state.positions = this.state.positions.map(position => {
      if (position.symbol === symbol) {
        return this.updatePositionPnL(position, data.price)
      }
      return position
    })

    this.updateAccountInfo()
    this.notifySubscribers()
  }

  // Calculate PnL for a position based on current market price
  private calculatePnL(position: Position, currentPrice: number): { pnl: number; pnlPercent: number } {
    const priceDiff = position.side === 'LONG'
      ? currentPrice - position.entryPrice
      : position.entryPrice - currentPrice

    const pnl = priceDiff * position.size
    const pnlPercent = (pnl / position.margin) * 100

    return { pnl, pnlPercent }
  }

  // Update position PnL based on current market price
  private updatePositionPnL(position: Position, currentPrice: number): Position {
    const { pnl, pnlPercent } = this.calculatePnL(position, currentPrice)

    return {
      ...position,
      markPrice: currentPrice,
      pnl,
      pnlPercent
    }
  }

  // Place a new order
  async placeOrder(orderRequest: OrderRequest): Promise<string> {
    // Check authentication with detailed logging
    const user = userService.getFirebaseUser()
    console.log('Authentication check:', {
      hasUser: !!user,
      userId: user?.uid,
      userEmail: user?.email,
      userServiceUser: userService.getUser()
    })

    if (!user) {
      console.error('Order placement failed: User not authenticated')
      throw new Error('User not authenticated. Please sign in and try again.')
    }

    // Additional check to ensure user token is valid
    try {
      await user.getIdToken(true) // Force refresh token
      console.log('User token refreshed successfully')
    } catch (tokenError) {
      console.error('Token refresh failed:', tokenError)
      throw new Error('Authentication token expired. Please sign in again.')
    }

    // Validate order request
    if (!orderRequest.symbol || !orderRequest.side || !orderRequest.type || !orderRequest.quantity) {
      console.error('Order placement failed: Invalid order parameters', orderRequest)
      throw new Error('Invalid order parameters. Please check your order details.')
    }

    if (orderRequest.quantity <= 0) {
      console.error('Order placement failed: Invalid quantity', orderRequest.quantity)
      throw new Error('Order quantity must be greater than 0.')
    }

    try {
      this.state.isLoading = true
      this.notifySubscribers()

      console.log('Placing order:', orderRequest)

      // Get current price with fallback
      let currentPrice = this.getMarketData(orderRequest.symbol)?.price

      if (!currentPrice && orderRequest.price) {
        currentPrice = orderRequest.price
      }

      if (!currentPrice) {
        // Fallback to a default price based on symbol (for demo purposes)
        const fallbackPrices = {
          'BTCUSDT': 43000,
          'ETHUSDT': 2500,
          'XRPUSDT': 0.6,
          'SOLUSDT': 100,
          'BNBUSDT': 300,
          'DOGEUSDT': 0.08,
          'ADAUSDT': 0.5,
          'TRXUSDT': 0.1
        }
        currentPrice = fallbackPrices[orderRequest.symbol] || 100
        console.warn(`Using fallback price for ${orderRequest.symbol}: ${currentPrice}`)
      }

      // Validate user balance for the order
      const userBalance = userService.getUserBalance()
      const orderValue = orderRequest.quantity * currentPrice
      const requiredMargin = orderValue / (orderRequest.leverage || 10)

      if (requiredMargin > userBalance) {
        console.error('Order placement failed: Insufficient balance', {
          requiredMargin,
          userBalance,
          orderValue
        })
        throw new Error(`Insufficient balance. Required: ${requiredMargin.toFixed(2)} USDT, Available: ${userBalance.toFixed(2)} USDT`)
      }

      const orderData = {
        symbol: orderRequest.symbol,
        side: orderRequest.side,
        type: orderRequest.type,
        price: orderRequest.price || currentPrice,
        origQty: orderRequest.quantity,
        executedQty: 0,
        status: 'NEW' as const,
        leverage: orderRequest.leverage || 10,
        ...(orderRequest.stopLoss && { stopLoss: orderRequest.stopLoss }),
        ...(orderRequest.takeProfit && { takeProfit: orderRequest.takeProfit })
      }

      console.log('Order data prepared:', orderData)

      // Add order to Firestore
      const orderId = await firestoreService.addOrder(user.uid, orderData)
      console.log('Order added to Firestore with ID:', orderId)

      // For market orders, execute immediately
      if (orderRequest.type === 'MARKET') {
        console.log('Executing market order immediately')
        await this.executeOrder(orderId, orderData, currentPrice)

        // Create notification for order filled
        await notificationService.createTradeNotification(user.uid, 'order_filled', {
          symbol: orderRequest.symbol,
          side: orderRequest.side,
          price: currentPrice,
          quantity: orderRequest.quantity
        })
      } else {
        console.log('Limit order placed, waiting for execution')
      }

      this.state.isLoading = false
      this.state.error = null
      this.notifySubscribers()

      console.log('Order placement successful:', orderId)
      return orderId
    } catch (error) {
      console.error('Order placement error:', error)
      this.state.isLoading = false
      this.state.error = error instanceof Error ? error.message : 'Failed to place order'
      this.notifySubscribers()
      throw error
    }
  }

  // Execute an order (create position and trade)
  private async executeOrder(orderId: string, orderData: any, executionPrice: number): Promise<void> {
    const user = userService.getFirebaseUser()
    if (!user) {
      console.error('Cannot execute order: User not authenticated')
      throw new Error('User not authenticated')
    }

    try {
      console.log('Executing order:', { orderId, orderData, executionPrice })

      // Calculate commission (0.1% for demo)
      const commission = orderData.origQty * executionPrice * 0.001
      let realizedPnl = 0

      console.log('Calculated commission:', commission)

      // Create position
      const positionData = {
        symbol: orderData.symbol,
        side: orderData.side === 'BUY' ? 'LONG' as const : 'SHORT' as const,
        entryPrice: executionPrice,
        markPrice: executionPrice,
        size: orderData.origQty,
        margin: (orderData.origQty * executionPrice) / orderData.leverage,
        leverage: orderData.leverage,
        pnl: 0,
        pnlPercent: 0,
        liquidationPrice: this.calculateLiquidationPrice(
          executionPrice,
          orderData.side === 'BUY' ? 'LONG' : 'SHORT',
          orderData.leverage
        ),
        orderId: orderId,
        ...(orderData.stopLoss && { stopLoss: orderData.stopLoss }),
        ...(orderData.takeProfit && { takeProfit: orderData.takeProfit })
      }

      console.log('Creating position:', positionData)

      // Add position to Firestore
      const positionId = await firestoreService.addPosition(user.uid, positionData)
      console.log('Position created with ID:', positionId)

      // Create notification for position opened
      await notificationService.createTradeNotification(user.uid, 'position_opened', {
        symbol: orderData.symbol,
        side: positionData.side,
        size: orderData.origQty,
        entryPrice: executionPrice
      })

      // Create trade record
      const tradeData = {
        symbol: orderData.symbol,
        side: orderData.side,
        price: executionPrice,
        quantity: orderData.origQty,
        commission: commission,
        realizedPnl: realizedPnl,
        leverage: orderData.leverage,
        orderId: orderId
      }

      console.log('Creating trade record:', tradeData)

      // Add trade to Firestore
      const tradeId = await firestoreService.addTrade(user.uid, tradeData)
      console.log('Trade created with ID:', tradeId)

      // Update order status to FILLED
      console.log('Updating order status to FILLED')
      await firestoreService.updateOrder(orderId, {
        status: 'FILLED',
        executedQty: orderData.origQty
      })

      // Update user balance (deduct commission)
      if (commission > 0) {
        console.log('Updating user balance for commission')
        const currentBalance = userService.getUserBalance()
        await userService.updateBalance(
          currentBalance - commission,
          'commission',
          `Trading commission: ${commission.toFixed(2)} USDT`
        )
        console.log('Balance updated, commission deducted:', commission)
      }

      console.log('Order execution completed successfully')

    } catch (error) {
      console.error('Error executing order:', error)

      // Try to update order status to FAILED if possible
      try {
        await firestoreService.updateOrder(orderId, {
          status: 'FAILED'
        })
      } catch (updateError) {
        console.error('Failed to update order status to FAILED:', updateError)
      }

      throw error
    }
  }

  private calculateLiquidationPrice(entryPrice: number, side: 'LONG' | 'SHORT', leverage: number): number {
    const maintenanceMarginRate = 0.005 // 0.5%
    const liquidationBuffer = 1 - maintenanceMarginRate - (1 / leverage)

    if (side === 'LONG') {
      return entryPrice * liquidationBuffer
    } else {
      return entryPrice / liquidationBuffer
    }
  }

  // Cancel an order
  async cancelOrder(orderId: string): Promise<void> {
    try {
      await firestoreService.updateOrder(orderId, { status: 'CANCELLED' })
    } catch (error) {
      console.error('Error cancelling order:', error)
      throw error
    }
  }

  // Close a position
  async closePosition(positionId: string): Promise<void> {
    const user = userService.getFirebaseUser()
    if (!user) return

    try {
      const position = this.state.positions.find(p => p.id === positionId)
      if (!position) return

      const currentPrice = this.getMarketData(position.symbol)?.price || position.markPrice

      // Create closing trade
      const tradeData = {
        symbol: position.symbol,
        side: position.side === 'LONG' ? 'SELL' : 'BUY',
        price: currentPrice,
        quantity: position.size,
        commission: position.size * currentPrice * 0.001,
        realizedPnl: position.pnl,
        leverage: position.leverage,
        orderId: position.orderId || '',
        positionId: positionId
      }

      // Add closing trade to Firestore
      await firestoreService.addTrade(user.uid, tradeData)

      // Update user balance with realized PnL
      const currentBalance = userService.getUserBalance()
      const newBalance = currentBalance + position.pnl - tradeData.commission
      await userService.updateBalance(
        newBalance,
        position.pnl > 0 ? 'trade_profit' : 'trade_loss',
        `Position closed: ${position.pnl > 0 ? '+' : ''}${position.pnl.toFixed(2)} USDT`
      )

      // Remove position from Firestore
      await firestoreService.deletePosition(positionId)

      // Create notification for position closed
      await notificationService.createTradeNotification(user.uid, 'position_closed', {
        symbol: position.symbol,
        side: position.side,
        pnl: position.pnl,
        closePrice: currentPrice
      })

    } catch (error) {
      console.error('Error closing position:', error)
      throw error
    }
  }

  // Cleanup method
  destroy() {
    if (this.unsubscribePositions) {
      this.unsubscribePositions()
    }
    if (this.unsubscribeOrders) {
      this.unsubscribeOrders()
    }
    if (this.userUnsubscribe) {
      this.userUnsubscribe()
    }
    if (this.userServiceUnsubscribe) {
      this.userServiceUnsubscribe()
    }
  }
}

// Export singleton instance
export const firebaseTradingService = new FirebaseTradingService()
export default firebaseTradingService
