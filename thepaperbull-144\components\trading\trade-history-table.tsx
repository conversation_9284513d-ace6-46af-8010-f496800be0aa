"use client"

import { useTrading } from "../../contexts/trading-context"

export default function TradeHistoryTable() {
  const { trades, isLoading } = useTrading()



  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp)
    return date.toLocaleString([], {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  if (isLoading) {
    return (
      <div className="bg-card rounded-lg border border-border p-3">
        <div className="flex justify-center py-6">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  if (trades.length === 0) {
    return (
      <div className="bg-card rounded-lg border border-border p-3">
        <div className="text-center py-6 text-muted-foreground text-sm">No trade history</div>
      </div>
    )
  }

  return (
    <div className="bg-card rounded-lg border border-border p-3">
      <div className="overflow-x-auto">
        <table className="w-full text-xs">
          <thead>
            <tr className="border-b border-border">
              <th className="text-left py-2 font-medium text-muted-foreground">Time</th>
              <th className="text-left py-2 font-medium text-muted-foreground">Symbol</th>
              <th className="text-left py-2 font-medium text-muted-foreground">Side</th>
              <th className="text-left py-2 font-medium text-muted-foreground">Price</th>
              <th className="text-left py-2 font-medium text-muted-foreground">Quantity</th>
              <th className="text-left py-2 font-medium text-muted-foreground">Leverage</th>
              <th className="text-left py-2 font-medium text-muted-foreground">Fee</th>
              <th className="text-right py-2 font-medium text-muted-foreground">Realized PnL</th>
            </tr>
          </thead>
          <tbody>
            {trades.map((trade) => (
              <tr key={trade.id} className="border-b border-border/50 hover:bg-muted/30">
                <td className="py-2">{formatTime(trade.timestamp)}</td>
                <td className="py-2">
                  <span className="font-medium">{trade.symbol}</span>
                </td>
                <td className="py-2">
                  <span className={trade.side === "BUY" ? "text-emerald-500" : "text-red-500"}>{trade.side}</span>
                </td>
                <td className="py-2">{trade.price.toFixed(2)}</td>
                <td className="py-2">{trade.quantity.toFixed(4)}</td>
                <td className="py-2 font-medium">{trade.leverage}x</td>
                <td className="py-2">{trade.commission.toFixed(2)} USD</td>
                <td className="py-2 text-right">
                  <span
                    className={
                      trade.realizedPnl > 0
                        ? "text-emerald-500"
                        : trade.realizedPnl < 0
                          ? "text-red-500"
                          : ""
                    }
                  >
                    {trade.realizedPnl > 0 ? "+" : ""}
                    {trade.realizedPnl.toFixed(2)} USD
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}
