(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[305],{12087:(e,r,t)=>{Promise.resolve().then(t.bind(t,83373))},25552:(e,r,t)=>{"use strict";t.d(r,{G6:()=>l,Ru:()=>i,bk:()=>n,signOutUser:()=>d});var a=t(16203),s=t(98915);let o=new a.HF;o.setCustomParameters({prompt:"select_account"});let n=async(e,r,t)=>{try{let o=await (0,a.eJ)(s.j2,e,r);return t&&o.user&&await (0,a.r7)(o.user,{displayName:t}),o.user}catch(e){throw console.error("Sign up error:",e),Error(c(e.code))}},i=async(e,r)=>{try{return(await (0,a.x9)(s.j2,e,r)).user}catch(e){throw console.error("Sign in error:",e),Error(c(e.code))}},l=async()=>{try{return(await (0,a.df)(s.j2,o)).user}catch(e){if(console.error("Google sign in error:",e),"auth/popup-closed-by-user"===e.code||"auth/cancelled-popup-request"===e.code)throw e;throw Error(c(e.code))}},d=async()=>{try{await (0,a.CI)(s.j2)}catch(e){throw console.error("Sign out error:",e),Error("Failed to sign out")}},c=e=>{switch(e){case"auth/user-not-found":return"No account found with this email address. Please check your email or sign up for a new account.";case"auth/wrong-password":return"Incorrect password. Please try again or reset your password.";case"auth/invalid-credential":return"Invalid email or password. Please check your credentials and try again.";case"auth/email-already-in-use":return"An account with this email already exists. Please sign in instead.";case"auth/weak-password":return"Password should be at least 6 characters long.";case"auth/invalid-email":return"Please enter a valid email address.";case"auth/user-disabled":return"This account has been disabled. Please contact support.";case"auth/too-many-requests":return"Too many failed attempts. Please try again later or reset your password.";case"auth/popup-closed-by-user":return"Sign-in popup was closed. Please try again.";case"auth/popup-blocked":return"Sign-in popup was blocked. Please allow popups and try again.";case"auth/network-request-failed":return"Network error. Please check your internet connection and try again.";case"auth/cancelled-popup-request":return"Sign-in was cancelled.";case"auth/account-exists-with-different-credential":return"An account already exists with the same email address but different sign-in credentials.";case"auth/operation-not-allowed":return"This sign-in method is not enabled. Please contact support.";case"auth/invalid-api-key":return"Invalid API key. Please check your Firebase configuration.";case"auth/app-deleted":return"Firebase app has been deleted. Please check your configuration.";case"auth/invalid-user-token":return"User token is invalid. Please sign in again.";case"auth/user-token-expired":return"User token has expired. Please sign in again.";default:return"Authentication error: ".concat(e||"Unknown error")}}},50395:(e,r,t)=>{"use strict";t.d(r,{A:()=>c,O:()=>d});var a=t(95155),s=t(12115),o=t(16203),n=t(98915),i=t(25552);let l=(0,s.createContext)(void 0);function d(e){let{children:r}=e,[t,d]=(0,s.useState)(null),[c,m]=(0,s.useState)(!0);(0,s.useEffect)(()=>{let e=(0,o.hg)(n.j2,e=>{d(e),m(!1)});return()=>e()},[]);let u=async(e,r)=>{try{return await (0,i.Ru)(e,r)}catch(e){throw console.error("Sign in error:",e),e}},x=async()=>{try{await (0,i.signOutUser)()}catch(e){throw console.error("Sign out error:",e),e}},h=async()=>{try{return await (0,i.G6)()}catch(e){throw console.error("Google sign in error:",e),e}};return(0,a.jsx)(l.Provider,{value:{user:t,loading:c,signIn:u,signOut:x,signInWithGoogle:h},children:r})}function c(){let e=(0,s.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},83373:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>_});var a=t(95155),s=t(12115),o=t(35695),n=t(6874),i=t.n(n),l=t(57340),d=t(46697),c=t(47863),m=t(66474),u=t(39785),x=t(72713),h=t(81497),p=t(381),g=t(34835),f=t(97636),v=t(62098),b=t(93509);function w(){let{theme:e,setTheme:r}=(0,f.D)();return(0,a.jsx)("button",{onClick:()=>r("dark"===e?"light":"dark"),className:"relative p-2.5 rounded-xl bg-gradient-to-r from-primary/10 via-primary/5 to-primary/10 hover:from-primary/20 hover:via-primary/10 hover:to-primary/20 border border-primary/20 hover:border-primary/30 transition-all duration-300 group shadow-md hover:shadow-lg","aria-label":"dark"===e?"Switch to light theme":"Switch to dark theme",children:(0,a.jsx)("div",{className:"relative",children:"dark"===e?(0,a.jsx)(v.A,{className:"h-4 w-4 text-yellow-500 group-hover:scale-110 group-hover:rotate-180 transition-all duration-300"}):(0,a.jsx)(b.A,{className:"h-4 w-4 text-blue-600 dark:text-blue-400 group-hover:scale-110 group-hover:-rotate-12 transition-all duration-300"})})})}function y(e){let{onCloseMobile:r}=e,n=(0,o.usePathname)(),[f,v]=(0,s.useState)("trading"),b=e=>{v(f===e?null:e)},y=()=>{r&&r()};return(0,a.jsxs)("div",{className:"h-full flex flex-col bg-gradient-to-br from-card via-card/98 to-muted/20 text-card-foreground backdrop-blur-md border-r border-border/30 shadow-2xl",children:[(0,a.jsx)("div",{className:"flex-1 overflow-auto py-responsive",children:(0,a.jsxs)("nav",{className:"px-responsive space-y-3",children:[(0,a.jsx)("div",{className:"px-2 mb-4",children:(0,a.jsx)("h3",{className:"text-2xs font-bold text-muted-foreground uppercase tracking-wider",children:"Navigation"})}),(0,a.jsx)("div",{children:(0,a.jsxs)(i(),{href:"/trade",className:"group flex items-center px-4 py-3.5 text-sm rounded-2xl transition-all duration-300 ".concat("/trade"===n?"bg-gradient-to-r from-primary via-primary/95 to-primary/80 text-primary-foreground font-semibold shadow-xl shadow-primary/30 scale-105":"text-foreground hover:bg-gradient-to-r hover:from-muted/60 hover:to-muted/40 hover:shadow-lg hover:scale-102"),onClick:y,children:[(0,a.jsx)("div",{className:"mr-3 p-1.5 rounded-lg transition-all duration-300 ".concat("/trade"===n?"bg-primary-foreground/20":"group-hover:bg-primary/10"),children:(0,a.jsx)(l.A,{className:"h-4 w-4"})}),(0,a.jsx)("span",{className:"font-medium",children:"Trade"}),"/trade"===n&&(0,a.jsx)("div",{className:"ml-auto w-2 h-2 bg-primary-foreground rounded-full animate-pulse"})]})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("button",{className:"group w-full flex items-center justify-between px-4 py-3.5 text-sm rounded-2xl text-foreground hover:bg-gradient-to-r hover:from-muted/60 hover:to-muted/40 hover:shadow-lg transition-all duration-300",onClick:()=>b("trading"),children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"mr-3 p-1.5 rounded-lg group-hover:bg-primary/10 transition-all duration-300",children:(0,a.jsx)(d.A,{className:"h-4 w-4"})}),(0,a.jsx)("span",{className:"font-medium",children:"Trading"})]}),"trading"===f?(0,a.jsx)(c.A,{className:"h-4 w-4 text-muted-foreground transition-transform duration-300 group-hover:text-foreground"}):(0,a.jsx)(m.A,{className:"h-4 w-4 text-muted-foreground transition-transform duration-300 group-hover:text-foreground"})]}),"trading"===f&&(0,a.jsxs)("div",{className:"mt-3 ml-8 space-y-2 animate-in slide-in-from-top-3 duration-300",children:[(0,a.jsxs)(i(),{href:"/trade",className:"group flex items-center justify-between px-4 py-3 text-sm rounded-xl transition-all duration-300 ".concat("/trade"===n?"bg-gradient-to-r from-primary/30 via-primary/25 to-primary/20 text-primary font-bold border border-primary/40 shadow-lg shadow-primary/20 scale-105":"text-foreground hover:text-primary hover:bg-gradient-to-r hover:from-primary/10 hover:to-primary/5 hover:border hover:border-primary/20 hover:shadow-md hover:scale-102 font-semibold"),onClick:y,children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"mr-3 p-1.5 rounded-lg transition-all duration-300 ".concat("/trade"===n?"bg-primary/20 text-primary":"bg-primary/10 text-primary group-hover:bg-primary/15"),children:(0,a.jsx)(d.A,{className:"h-3.5 w-3.5"})}),(0,a.jsx)("span",{children:"Futures"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:["/trade"===n&&(0,a.jsx)("div",{className:"w-2 h-2 bg-primary rounded-full animate-pulse"}),(0,a.jsx)("span",{className:"text-xs bg-gradient-to-r from-emerald-500/20 to-emerald-600/20 text-emerald-600 dark:text-emerald-400 px-2.5 py-1 rounded-lg font-bold border border-emerald-500/30 shadow-sm",children:"LIVE"})]})]}),(0,a.jsxs)(i(),{href:"#",className:"group flex items-center justify-between px-4 py-2.5 text-sm rounded-xl text-muted-foreground hover:text-foreground hover:bg-gradient-to-r hover:from-muted/40 hover:to-muted/20 transition-all duration-300",onClick:y,children:[(0,a.jsx)("span",{children:"Margin"}),(0,a.jsx)("span",{className:"text-xs bg-gradient-to-r from-amber-500/20 to-amber-600/20 text-amber-600 dark:text-amber-400 px-2.5 py-1 rounded-lg font-medium border border-amber-500/20",children:"Soon"})]}),(0,a.jsxs)(i(),{href:"#",className:"group flex items-center justify-between px-4 py-2.5 text-sm rounded-xl text-muted-foreground hover:text-foreground hover:bg-gradient-to-r hover:from-muted/40 hover:to-muted/20 transition-all duration-300",onClick:y,children:[(0,a.jsx)("span",{children:"Options"}),(0,a.jsx)("span",{className:"text-xs bg-gradient-to-r from-amber-500/20 to-amber-600/20 text-amber-600 dark:text-amber-400 px-2.5 py-1 rounded-lg font-medium border border-amber-500/20",children:"Soon"})]})]})]}),(0,a.jsx)("div",{children:(0,a.jsxs)(i(),{href:"/wallet",className:"group flex items-center px-4 py-3.5 text-sm rounded-2xl transition-all duration-300 ".concat("/wallet"===n?"bg-gradient-to-r from-primary via-primary/95 to-primary/80 text-primary-foreground font-semibold shadow-xl shadow-primary/30 scale-105":"text-foreground hover:bg-gradient-to-r hover:from-muted/60 hover:to-muted/40 hover:shadow-lg hover:scale-102"),onClick:y,children:[(0,a.jsx)("div",{className:"mr-3 p-1.5 rounded-lg transition-all duration-300 ".concat("/wallet"===n?"bg-primary-foreground/20":"group-hover:bg-primary/10"),children:(0,a.jsx)(u.A,{className:"h-4 w-4"})}),(0,a.jsx)("span",{className:"font-medium",children:"Wallet"}),"/wallet"===n&&(0,a.jsx)("div",{className:"ml-auto w-2 h-2 bg-primary-foreground rounded-full animate-pulse"})]})}),(0,a.jsx)("div",{children:(0,a.jsxs)(i(),{href:"/statistics",className:"group flex items-center px-4 py-3.5 text-sm rounded-2xl transition-all duration-300 ".concat("/statistics"===n?"bg-gradient-to-r from-primary via-primary/95 to-primary/80 text-primary-foreground font-semibold shadow-xl shadow-primary/30 scale-105":"text-foreground hover:bg-gradient-to-r hover:from-muted/60 hover:to-muted/40 hover:shadow-lg hover:scale-102"),onClick:y,children:[(0,a.jsx)("div",{className:"mr-3 p-1.5 rounded-lg transition-all duration-300 ".concat("/statistics"===n?"bg-primary-foreground/20":"group-hover:bg-primary/10"),children:(0,a.jsx)(x.A,{className:"h-4 w-4"})}),(0,a.jsx)("span",{className:"font-medium",children:"Statistics"}),"/statistics"===n&&(0,a.jsx)("div",{className:"ml-auto w-2 h-2 bg-primary-foreground rounded-full animate-pulse"})]})}),(0,a.jsx)("div",{children:(0,a.jsxs)(i(),{href:"/chat",className:"group flex items-center px-4 py-3.5 text-sm rounded-2xl transition-all duration-300 ".concat("/chat"===n?"bg-gradient-to-r from-primary via-primary/95 to-primary/80 text-primary-foreground font-semibold shadow-xl shadow-primary/30 scale-105":"text-foreground hover:bg-gradient-to-r hover:from-muted/60 hover:to-muted/40 hover:shadow-lg hover:scale-102"),onClick:y,children:[(0,a.jsx)("div",{className:"mr-3 p-1.5 rounded-lg transition-all duration-300 ".concat("/chat"===n?"bg-primary-foreground/20":"group-hover:bg-primary/10"),children:(0,a.jsx)(h.A,{className:"h-4 w-4"})}),(0,a.jsx)("span",{className:"font-medium",children:"AI Chat"}),"/chat"===n&&(0,a.jsx)("div",{className:"ml-auto w-2 h-2 bg-primary-foreground rounded-full animate-pulse"})]})}),(0,a.jsx)("div",{children:(0,a.jsxs)(i(),{href:"/settings",className:"group flex items-center px-4 py-3.5 text-sm rounded-2xl transition-all duration-300 ".concat("/settings"===n?"bg-gradient-to-r from-primary via-primary/95 to-primary/80 text-primary-foreground font-semibold shadow-xl shadow-primary/30 scale-105":"text-foreground hover:bg-gradient-to-r hover:from-muted/60 hover:to-muted/40 hover:shadow-lg hover:scale-102"),onClick:y,children:[(0,a.jsx)("div",{className:"mr-3 p-1.5 rounded-lg transition-all duration-300 ".concat("/settings"===n?"bg-primary-foreground/20":"group-hover:bg-primary/10"),children:(0,a.jsx)(p.A,{className:"h-4 w-4"})}),(0,a.jsx)("span",{className:"font-medium",children:"Settings"}),"/settings"===n&&(0,a.jsx)("div",{className:"ml-auto w-2 h-2 bg-primary-foreground rounded-full animate-pulse"})]})})]})}),(0,a.jsx)("div",{className:"p-responsive border-t border-border/30 bg-gradient-to-br from-muted/30 via-muted/20 to-background/50 backdrop-blur-sm",children:(0,a.jsxs)("div",{className:"space-y-3 sm:space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-2xl bg-gradient-to-r from-background/60 to-muted/40 border border-border/30",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-primary rounded-full animate-pulse"}),(0,a.jsx)("span",{className:"text-responsive-xs font-semibold text-foreground",children:"Theme"})]}),(0,a.jsx)(w,{})]}),(0,a.jsx)("div",{className:"flex items-center justify-center",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("span",{className:"bg-gradient-to-r from-primary/25 via-primary/20 to-primary/15 text-primary px-3 py-1.5 sm:px-4 sm:py-2 rounded-2xl text-2xs sm:text-xs font-bold border border-primary/30 shadow-lg shadow-primary/10",children:"BETA VERSION"}),(0,a.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-emerald-400 to-emerald-500 rounded-full border-2 border-background animate-bounce"})]})}),(0,a.jsxs)("button",{onClick:async()=>{try{let{signOutUser:e}=await Promise.resolve().then(t.bind(t,25552));await e(),localStorage.removeItem("rememberedUsername"),window.location.href="/login"}catch(e){console.error("Logout error:",e),window.location.href="/login"}},className:"touch-target group flex items-center justify-center px-4 py-3.5 text-responsive-xs rounded-2xl bg-gradient-to-r from-red-500/15 via-red-500/10 to-red-600/15 text-red-600 dark:text-red-400 hover:from-red-500/25 hover:via-red-500/20 hover:to-red-600/25 border border-red-500/25 hover:border-red-500/40 transition-all duration-300 w-full shadow-lg hover:shadow-xl hover:scale-105",children:[(0,a.jsx)("div",{className:"mr-3 p-1 rounded-lg bg-red-500/20 group-hover:bg-red-500/30 transition-all duration-300",children:(0,a.jsx)(g.A,{className:"h-4 w-4 group-hover:scale-110 transition-transform duration-300"})}),(0,a.jsx)("span",{className:"font-semibold",children:"Logout"})]})]})})]})}var j=t(85339),N=t(79772),k=t(81586),A=t(94788),S=t(34135),E=t(50395),C=t(50475);function P(){var e,r;let[t,n]=(0,s.useState)(!1),[i,l]=(0,s.useState)(null),[d,c]=(0,s.useState)(!0),[m,x]=(0,s.useState)(null),h=(0,s.useRef)(null),f=(0,o.useRouter)(),{user:v,signOut:b}=(0,E.A)(),{accountInfo:w,getTotalPnL:y,getAvailableBalance:P}=(0,S.fx)();(0,s.useEffect)(()=>(c(!0),x(null),C.A.subscribe(e=>{try{l(e),c(!1),x(null)}catch(e){console.error("Error updating user data:",e),x("Failed to load user data"),c(!1)}})),[]);let L=(()=>{var e,r;let t=(null==i?void 0:null===(e=i.balance)||void 0===e?void 0:e.current)||1e4,a=(null==w?void 0:w.totalWalletBalance)||1e4,s=t||a||1e4,o=P();if(null==o||0===o){let e=y()||0;o=Math.max(0,s-((null==w?void 0:w.totalPositionInitialMargin)||0)+e)}return{totalBalance:s,availableBalance:o,totalPnL:y()||0,currency:(null==i?void 0:null===(r=i.balance)||void 0===r?void 0:r.currency)||"USDT"}})();(0,s.useEffect)(()=>{function e(e){h.current&&!h.current.contains(e.target)&&n(!1)}return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]);let I=async()=>{try{await b(),f.push("/login")}catch(e){console.error("Sign out error:",e)}},T=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"sm";return d?(0,a.jsx)("div",{className:"".concat("sm"===e?"w-10 h-10":"w-16 h-16"," bg-muted rounded-full animate-pulse")}):(null==i||i.avatar,(0,a.jsx)("div",{className:"".concat({sm:"w-10 h-10 text-sm",lg:"w-16 h-16 text-xl"}["sm"===e?"sm":"lg"]," rounded-full bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center shadow-md border-2 border-white dark:border-gray-700 transition-all duration-200 hover:shadow-lg"),children:(0,a.jsx)("span",{className:"select-none",children:"\uD83D\uDC02"})}))};return(0,a.jsxs)("div",{className:"relative",ref:h,children:[(0,a.jsx)("button",{className:"rounded-full transition-all duration-200 ".concat(t?"ring-2 ring-primary/30":"hover:scale-105"," ").concat(d?"opacity-75":""),onClick:()=>n(!t),disabled:d,children:T("sm")}),t&&(0,a.jsxs)("div",{className:"absolute right-0 mt-2 w-56 rounded-md shadow-xl bg-card ring-1 ring-black/5 dark:ring-white/10 z-[100] animate-in fade-in slide-in-from-top-5 duration-200",children:[(0,a.jsxs)("div",{className:"py-2 px-3 border-b border-border",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[T("xl"),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:(null==i?void 0:i.name)||(null==v?void 0:v.displayName)||"User"}),(null==i?void 0:null===(e=i.subscription)||void 0===e?void 0:e.type)&&"free"!==i.subscription.type&&(0,a.jsx)("span",{className:"text-xs bg-primary/20 text-primary px-1.5 py-0.5 rounded-md uppercase",children:i.subscription.type})]}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:(null==i?void 0:i.email)||(null==v?void 0:v.email)||"<EMAIL>"})]})]}),m&&(0,a.jsx)("div",{className:"mt-3 pt-2 border-t border-border/50",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-red-500",children:[(0,a.jsx)(j.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-xs",children:m})]})}),!m&&(0,a.jsxs)("div",{className:"mt-3 pt-2 border-t border-border/50",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:"Total Balance"}),d?(0,a.jsx)("div",{className:"h-4 w-16 bg-muted rounded animate-pulse"}):(0,a.jsxs)("span",{className:"text-sm font-medium",children:[L.totalBalance.toFixed(2)," ",L.currency]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:"Available"}),d?(0,a.jsx)("div",{className:"h-4 w-16 bg-muted rounded animate-pulse"}):(0,a.jsxs)("span",{className:"text-sm font-medium",children:[L.availableBalance.toFixed(2)," ",L.currency]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:"Unrealized PnL"}),d?(0,a.jsx)("div",{className:"h-4 w-16 bg-muted rounded animate-pulse"}):(0,a.jsxs)("span",{className:"text-sm font-medium ".concat(L.totalPnL>=0?"text-emerald-500":"text-red-500"),children:[L.totalPnL>=0?"+":"",L.totalPnL.toFixed(2)," ",L.currency]})]}),(null==i?void 0:null===(r=i.balance)||void 0===r?void 0:r.lastUpdated)&&(0,a.jsx)("div",{className:"mt-2 pt-1 border-t border-border/30",children:(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:["Updated: ",new Date(i.balance.lastUpdated).toLocaleTimeString()]})})]})]}),(0,a.jsxs)("div",{className:"py-1",children:[(0,a.jsxs)("button",{className:"flex items-center w-full px-4 py-2 text-sm text-foreground hover:bg-muted transition-colors duration-150",onClick:()=>{n(!1),f.push("/settings")},children:[(0,a.jsx)(N.A,{className:"h-4 w-4 mr-3 text-muted-foreground"}),"My Profile"]}),(0,a.jsxs)("button",{className:"flex items-center w-full px-4 py-2 text-sm text-foreground hover:bg-muted transition-colors duration-150",onClick:()=>{n(!1),f.push("/wallet")},children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-3 text-muted-foreground"}),"Wallet"]}),(0,a.jsxs)("button",{className:"flex items-center w-full px-4 py-2 text-sm text-foreground hover:bg-muted transition-colors duration-150",onClick:()=>{n(!1),f.push("/settings?tab=payment")},children:[(0,a.jsx)(k.A,{className:"h-4 w-4 mr-3 text-muted-foreground"}),"Billing"]}),(0,a.jsxs)("button",{className:"flex items-center w-full px-4 py-2 text-sm text-foreground hover:bg-muted transition-colors duration-150",onClick:()=>{n(!1),f.push("/settings")},children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-3 text-muted-foreground"}),"Settings"]}),(0,a.jsxs)("button",{className:"flex items-center w-full px-4 py-2 text-sm text-foreground hover:bg-muted transition-colors duration-150",onClick:()=>{n(!1),window.open("mailto:<EMAIL>","_blank")},children:[(0,a.jsx)(A.A,{className:"h-4 w-4 mr-3 text-muted-foreground"}),"Help Center"]})]}),(0,a.jsx)("div",{className:"py-1 border-t border-border",children:(0,a.jsxs)("button",{className:"flex items-center w-full px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-muted transition-colors duration-150",onClick:I,children:[(0,a.jsx)(g.A,{className:"h-4 w-4 mr-3"}),"Sign out"]})})]})]})}var L=t(54416),I=t(74783);function T(e){let{onToggle:r,isOpen:t}=e;return(0,a.jsx)("button",{className:"lg:hidden touch-target rounded-md hover:bg-muted transition-colors",onClick:()=>r(!t),"aria-label":t?"Close sidebar":"Open sidebar",children:t?(0,a.jsx)(L.A,{className:"h-6 w-6 text-foreground"}):(0,a.jsx)(I.A,{className:"h-6 w-6 text-foreground"})})}var z=t(33109),B=t(40646),U=t(1243),D=t(81284),M=t(23861),O=t(5196),R=t(27759),F=t(50757);function W(e){let{isOpen:r,onToggle:t,onClose:o}=e,{user:n}=(0,E.A)(),[i,l]=(0,s.useState)([]),[d,c]=(0,s.useState)(0),m=(0,s.useRef)(null);(0,s.useEffect)(()=>{R.A.initialize(n)},[n]),(0,s.useEffect)(()=>R.A.subscribe(e=>{l(e),c(R.A.getUnreadCount())}),[]),(0,s.useEffect)(()=>{function e(e){m.current&&!m.current.contains(e.target)&&o()}if(r)return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[r,o]);let u=async e=>{await R.A.markAsRead(e)},x=async()=>{await R.A.markAllAsRead()},h=e=>{switch(e){case"trade":return(0,a.jsx)(z.A,{className:"h-4 w-4 text-blue-500"});case"success":return(0,a.jsx)(B.A,{className:"h-4 w-4 text-green-500"});case"warning":return(0,a.jsx)(U.A,{className:"h-4 w-4 text-yellow-500"});case"error":return(0,a.jsx)(U.A,{className:"h-4 w-4 text-red-500"});default:return(0,a.jsx)(D.A,{className:"h-4 w-4 text-blue-500"})}},p=e=>{if(!e)return"Just now";try{let r=e.toDate?e.toDate():new Date(e);return(0,F.m)(r,{addSuffix:!0})}catch(e){return"Just now"}};return(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("button",{onClick:t,className:"touch-target relative text-muted-foreground hover:text-foreground transition-colors","aria-label":"Notifications",children:[(0,a.jsx)(M.A,{className:"h-5 w-5"}),d>0&&(0,a.jsx)("span",{className:"absolute -top-0.5 -right-0.5 bg-primary text-primary-foreground text-2xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse font-semibold",children:d>99?"99+":d})]}),r&&(0,a.jsxs)("div",{ref:m,className:"absolute right-0 mt-2 w-80 max-w-[calc(100vw-2rem)] bg-card rounded-lg shadow-xl border border-border z-[100] animate-in fade-in slide-in-from-top-5 duration-200",children:[(0,a.jsx)("div",{className:"p-3 border-b border-border",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"font-semibold text-responsive-sm",children:"Notifications"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[d>0&&(0,a.jsx)("button",{onClick:x,className:"text-2xs text-primary hover:text-primary/90 cursor-pointer font-medium",children:"Mark all as read"}),(0,a.jsx)("button",{onClick:o,className:"text-muted-foreground hover:text-foreground p-1 rounded-md hover:bg-muted transition-colors",children:(0,a.jsx)(L.A,{className:"h-4 w-4"})})]})]})}),(0,a.jsx)("div",{className:"max-h-[400px] overflow-y-auto",children:0===i.length?(0,a.jsxs)("div",{className:"p-6 text-center",children:[(0,a.jsx)(M.A,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-responsive-xs text-muted-foreground",children:"No notifications yet"}),(0,a.jsx)("p",{className:"text-2xs text-muted-foreground mt-1",children:"You'll see trading updates and system notifications here"})]}):(0,a.jsx)("div",{className:"divide-y divide-border",children:i.slice(0,10).map(e=>(0,a.jsx)("div",{className:"p-3 hover:bg-muted/50 transition-colors ".concat(e.read?"":"bg-primary/5 border-l-2 border-l-primary"),children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0 mt-0.5",children:h(e.type)}),(0,a.jsx)("div",{className:"flex-1 min-w-0",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-responsive-xs font-medium text-foreground",children:e.title}),(0,a.jsx)("p",{className:"text-2xs text-muted-foreground mt-1 line-clamp-2",children:e.message}),(0,a.jsx)("p",{className:"text-2xs text-muted-foreground mt-1",children:p(e.createdAt)})]}),!e.read&&(0,a.jsx)("button",{onClick:()=>u(e.id),className:"ml-2 p-1 text-muted-foreground hover:text-foreground rounded-md hover:bg-muted transition-colors",title:"Mark as read",children:(0,a.jsx)(O.A,{className:"h-3 w-3"})})]})})]})},e.id))})}),i.length>0&&(0,a.jsx)("div",{className:"p-2 border-t border-border text-center",children:(0,a.jsx)("button",{className:"text-responsive-xs text-primary hover:text-primary/90 font-medium",children:"View all notifications"})})]})]})}function _(e){let{children:r}=e,t=(0,o.useRouter)(),[n,i]=(0,s.useState)(!0),[l,d]=(0,s.useState)(!1),[c,m]=(0,s.useState)(!1),[u,x]=(0,s.useState)(!0),[h,p]=(0,s.useState)(!1),{user:g,loading:f}=(0,E.A)();return((0,s.useEffect)(()=>{p(!0),f||(g?i(!1):t.push("/login"))},[g,f,t]),(0,s.useEffect)(()=>{let e=e=>{let r=document.getElementById("mobile-sidebar"),t=document.getElementById("mobile-sidebar-toggle");l&&r&&t&&!r.contains(e.target)&&!t.contains(e.target)&&d(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[l]),(0,s.useEffect)(()=>(l?document.body.style.overflow="hidden":document.body.style.overflow="auto",()=>{document.body.style.overflow="auto"}),[l]),(0,s.useEffect)(()=>{function e(e){notificationDropdownRef.current&&!notificationDropdownRef.current.contains(e.target)&&m(!1)}return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),(0,s.useEffect)(()=>{let e=()=>{p(!0)};if(h)return window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[h]),n||f)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("svg",{className:"animate-spin h-12 w-12 text-primary mx-auto",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,a.jsx)("p",{className:"mt-4 text-lg font-medium text-foreground",children:"Loading..."})]})}):(0,a.jsx)(S.rM,{children:(0,a.jsx)("div",{className:"min-h-screen w-full bg-background text-foreground",children:(0,a.jsx)("div",{className:"h-full w-full",children:(0,a.jsxs)("div",{className:"bg-card shadow-lg h-full",children:[(0,a.jsxs)("header",{className:"relative flex items-center justify-between px-4 py-2 border-b border-border bg-card/50 backdrop-blur-sm z-50",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"lg:hidden",children:(0,a.jsx)(T,{onToggle:d,isOpen:l})}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("h1",{className:"text-primary font-bold text-xl tracking-tight bg-gradient-to-r from-primary via-primary/90 to-primary/70 bg-clip-text text-transparent",children:"ThePaperBull"}),(0,a.jsx)("span",{className:"ml-2 text-xs bg-gradient-to-r from-primary/20 to-primary/30 text-primary px-2 py-0.5 rounded-full font-semibold border border-primary/20",children:"BETA"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 sm:space-x-5 relative z-50",children:[(0,a.jsx)(W,{isOpen:c,onToggle:()=>m(!c),onClose:()=>m(!1)}),(0,a.jsx)(P,{})]})]}),(0,a.jsxs)("div",{className:"relative h-[calc(100vh-60px)]",children:[l&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-20 md:hidden"}),(0,a.jsx)("div",{id:"sidebar",className:"".concat(l?"translate-x-0":"-translate-x-full"," fixed inset-y-0 left-0 z-30 w-80 transform transition-all duration-300 ease-in-out md:translate-x-0 shadow-xl"),style:{top:"60px",height:"calc(100vh - 60px)",transform:h&&window.innerWidth>=768?"translateX(".concat(u?"0":"-100%",")"):l?"translateX(0)":"translateX(-100%)"},children:(0,a.jsx)(y,{onCloseMobile:()=>d(!1)})}),(0,a.jsx)("div",{className:"hidden md:flex fixed left-0 top-1/2 transform -translate-y-1/2 z-40",children:(0,a.jsx)("button",{onClick:()=>x(!u),className:"bg-gradient-to-r from-primary to-primary/80 text-primary-foreground rounded-r-xl p-3 shadow-lg hover:shadow-xl transition-all duration-300 group",style:{transform:"translateX(".concat(u?"320px":"0px",")")},"aria-label":u?"Hide sidebar":"Show sidebar",children:u?(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"group-hover:scale-110 transition-transform duration-200",children:(0,a.jsx)("path",{d:"m15 18-6-6 6-6"})}):(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"group-hover:scale-110 transition-transform duration-200",children:(0,a.jsx)("path",{d:"m9 18 6-6-6-6"})})})}),(0,a.jsx)("div",{className:"w-full h-full overflow-auto",children:(0,a.jsx)(s.Suspense,{fallback:(0,a.jsx)("div",{children:"Loading..."}),children:r})})]})]})})})})}},97636:(e,r,t)=>{"use strict";t.d(r,{D:()=>i,N:()=>n});var a=t(95155),s=t(12115);let o=(0,s.createContext)(void 0);function n(e){let{children:r}=e,[t,n]=(0,s.useState)("light"),[i,l]=(0,s.useState)("emerald"),[d,c]=(0,s.useState)("light"),[m,u]=(0,s.useState)(!1);return((0,s.useEffect)(()=>{u(!0);let e=localStorage.getItem("theme"),r=localStorage.getItem("colorScheme");e&&n(e),r&&l(r)},[]),(0,s.useEffect)(()=>{m&&(localStorage.setItem("theme",t),localStorage.setItem("colorScheme",i),"dark"===t||"system"===t&&window.matchMedia("(prefers-color-scheme: dark)").matches?(document.documentElement.classList.add("dark"),c("dark")):(document.documentElement.classList.remove("dark"),c("light")),document.documentElement.setAttribute("data-color-scheme",i))},[t,i,m]),(0,s.useEffect)(()=>{if(!m||"system"!==t)return;let e=window.matchMedia("(prefers-color-scheme: dark)"),r=()=>{e.matches?(document.documentElement.classList.add("dark"),c("dark")):(document.documentElement.classList.remove("dark"),c("light"))};return e.addEventListener("change",r),()=>e.removeEventListener("change",r)},[t,m]),m)?(0,a.jsx)(o.Provider,{value:{theme:t,setTheme:n,colorScheme:i,setColorScheme:l,resolvedTheme:d},children:r}):(0,a.jsx)(a.Fragment,{children:r})}function i(){let e=(0,s.useContext)(o);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}}},e=>{var r=r=>e(e.s=r);e.O(0,[992,965,879,914,471,475,135,441,684,358],()=>r(12087)),_N_E=e.O()}]);