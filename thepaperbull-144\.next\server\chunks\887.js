exports.id=887,exports.ids=[887],exports.modules={21643:(e,r,t)=>{"use strict";t.d(r,{A:()=>c});var s=t(83427),a=t(80043),i=t(50227),o=t(70695),n=t(38239);function l(e){return{id:e.id,email:e.email,name:e.name,createdAt:e.createdAt?.toMillis?.()||Date.now(),subscription:{type:e.subscription.type,status:e.subscription.status,startDate:e.subscription.startDate?.toMillis?.()||Date.now(),endDate:e.subscription.endDate?.toMillis?.(),features:e.subscription.features},balance:{current:e.balance.current,maximum:e.balance.maximum,currency:e.balance.currency,lastUpdated:e.balance.lastUpdated?.toMillis?.()||Date.now(),transactions:[]},settings:e.settings}}class d{constructor(){this.currentUser=null,this.firebaseUser=null,this.subscribers=[],this.unsubscribeAuth=null,this.unsubscribeProfile=null,this.transactions=[],this.initializeAuth()}initializeAuth(){this.unsubscribeAuth=(0,i.hg)(a.j2,async e=>{this.firebaseUser=e,e?await this.loadUserProfile(e.uid):(this.currentUser=null,this.transactions=[],this.unsubscribeProfile&&(this.unsubscribeProfile(),this.unsubscribeProfile=null),this.notifySubscribers())})}async loadUserProfile(e){try{let r=await s.b.getUserProfile(e);!r&&this.firebaseUser&&(r=await s.b.createUserProfile(e,this.firebaseUser.email||"",this.firebaseUser.displayName||"User"))&&await o.A.createWelcomeNotification(e),r&&(this.currentUser=l(r),this.transactions=await s.b.getUserTransactions(e),this.currentUser.balance.transactions=this.transactions.map(e=>({id:e.id,type:e.type,amount:e.amount,balance_before:e.balance_before,balance_after:e.balance_after,description:e.description,timestamp:e.timestamp?.toMillis?.()||Date.now()})),this.unsubscribeProfile&&this.unsubscribeProfile(),this.unsubscribeProfile=s.b.subscribeToUserProfile(e,e=>{e&&(this.currentUser=l(e),this.notifySubscribers())}),this.notifySubscribers())}catch(e){console.error("Error loading user profile:",e),"permission-denied"===e.code?(console.error("Permission denied - Firestore security rules may need to be updated"),this.firebaseUser&&(this.currentUser={id:this.firebaseUser.uid,email:this.firebaseUser.email||"",name:this.firebaseUser.displayName||"User",avatar:"bull-trader",balance:{current:1e4,maximum:5e4,currency:"USDT",lastUpdated:Date.now(),transactions:[]},subscription:{type:"free",status:"active"},settings:{},createdAt:Date.now(),lastLoginAt:Date.now()},this.notifySubscribers())):(this.currentUser=null,this.notifySubscribers())}}notifySubscribers(){this.subscribers.forEach(e=>e(this.currentUser))}getUser(){return this.currentUser}getUserBalance(){return this.currentUser?.balance?.current||1e4}getMaxBalance(){return this.currentUser?.balance.maximum||n.sC}getAvailableFunds(){return this.currentUser?this.currentUser.balance.maximum-this.currentUser.balance.current:0}canAddFunds(e){return this.currentUser?e<=0?{canAdd:!1,reason:"Amount must be positive"}:this.currentUser.balance.current+e>this.currentUser.balance.maximum?{canAdd:!1,reason:`Would exceed maximum balance of ${this.currentUser.balance.maximum.toLocaleString()} USDT`}:{canAdd:!0}:{canAdd:!1,reason:"User not found"}}async addFunds(e){if(!this.currentUser||!this.firebaseUser)return{success:!1,new_balance:0,requires_subscription:!1,message:"User not found"};let r=this.canAddFunds(e.amount);if(!r.canAdd){let t=this.currentUser.balance.current+e.amount<=n.ND.premium.maxBalance;return{success:!1,new_balance:this.currentUser.balance.current,requires_subscription:t,message:r.reason||"Cannot add funds"}}try{let r=this.currentUser.balance.current,t=r+e.amount;return await s.b.updateUserBalance(this.firebaseUser.uid,t),await s.b.addTransaction(this.firebaseUser.uid,{type:"deposit",amount:e.amount,balance_before:r,balance_after:t,description:"subscription_upgrade"===e.method?"Funds added via subscription":"Virtual funds added"}),{success:!0,new_balance:t,requires_subscription:!1,message:`Successfully added ${e.amount.toLocaleString()} USDT to your account`}}catch(e){return console.error("Error adding funds:",e),{success:!1,new_balance:this.currentUser.balance.current,requires_subscription:!1,message:"Failed to add funds. Please try again."}}}async upgradeSubscription(e){if(!this.currentUser||!this.firebaseUser)return!1;try{let r=n.ND[e];return await s.b.updateUserProfile(this.firebaseUser.uid,{subscription:{type:e,status:"active",startDate:new Date,endDate:new Date(Date.now()+31536e6),features:r.features},balance:{...this.currentUser.balance,maximum:r.maxBalance}}),!0}catch(e){return console.error("Error upgrading subscription:",e),!1}}async updateBalance(e,r,t){if(this.currentUser&&this.firebaseUser)try{let a=this.currentUser.balance.current;await s.b.updateUserBalance(this.firebaseUser.uid,e),await s.b.addTransaction(this.firebaseUser.uid,{type:r,amount:e-a,balance_before:a,balance_after:e,description:t})}catch(e){console.error("Error updating balance:",e)}}subscribe(e){return this.subscribers.push(e),e(this.currentUser),()=>{let r=this.subscribers.indexOf(e);r>-1&&this.subscribers.splice(r,1)}}getFirebaseUser(){return this.firebaseUser}destroy(){this.unsubscribeAuth&&this.unsubscribeAuth(),this.unsubscribeProfile&&this.unsubscribeProfile()}}let c=new d},38004:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i}),t(37413);var s=t(31658);let a={runtime:"edge",dynamic:"force-static",alt:"Cryptocurrency Trading Dashboard",size:{width:1200,height:630},contentType:"image/png"};async function i(e){let{__metadata_id__:r,...t}=await e.params,i=(0,s.fillMetadataSegment)(".",t,"opengraph-image"),{generateImageMetadata:o}=a;function n(e,r){let t={alt:e.alt,type:e.contentType||"image/png",url:i+(r?"/"+r:"")+"?53e13af1384a38e6"},{size:s}=e;return s&&(t.width=s.width,t.height=s.height),t}return o?(await o({params:t})).map((e,r)=>{let t=(e.id||r)+"";return n(e,t)}):[n(a,"")]}},38239:(e,r,t)=>{"use strict";t.d(r,{ND:()=>s,sC:()=>a});let s={free:{type:"free",name:"Free",maxBalance:5e4,price:0,features:{maxBalance:5e4,advancedAnalytics:!1,prioritySupport:!1,customIndicators:!1,apiAccess:!1}},premium:{type:"premium",name:"Premium",maxBalance:5e5,price:29.99,features:{maxBalance:5e5,advancedAnalytics:!0,prioritySupport:!1,customIndicators:!0,apiAccess:!1}},pro:{type:"pro",name:"Pro",maxBalance:1e6,price:99.99,features:{maxBalance:1e6,advancedAnalytics:!0,prioritySupport:!0,customIndicators:!0,apiAccess:!0}}},a=5e4},50579:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>z});var s=t(60687),a=t(43210),i=t(16189),o=t(85814),n=t.n(o),l=t(32192),d=t(20835),c=t(3589),u=t(78272),m=t(35583),h=t(53411),b=t(58887),p=t(84027),x=t(40083),g=t(25878),f=t(21134),y=t(363);function v(){let{theme:e,setTheme:r}=(0,g.D)();return(0,s.jsx)("button",{onClick:()=>r("dark"===e?"light":"dark"),className:"relative p-2.5 rounded-xl bg-gradient-to-r from-primary/10 via-primary/5 to-primary/10 hover:from-primary/20 hover:via-primary/10 hover:to-primary/20 border border-primary/20 hover:border-primary/30 transition-all duration-300 group shadow-md hover:shadow-lg","aria-label":"dark"===e?"Switch to light theme":"Switch to dark theme",children:(0,s.jsx)("div",{className:"relative",children:"dark"===e?(0,s.jsx)(f.A,{className:"h-4 w-4 text-yellow-500 group-hover:scale-110 group-hover:rotate-180 transition-all duration-300"}):(0,s.jsx)(y.A,{className:"h-4 w-4 text-blue-600 dark:text-blue-400 group-hover:scale-110 group-hover:-rotate-12 transition-all duration-300"})})})}function w({onCloseMobile:e}){let r=(0,i.usePathname)(),[o,g]=(0,a.useState)("trading"),f=e=>{g(o===e?null:e)},y=()=>{e&&e()};return(0,s.jsxs)("div",{className:"h-full flex flex-col bg-gradient-to-br from-card via-card/98 to-muted/20 text-card-foreground backdrop-blur-md border-r border-border/30 shadow-2xl",children:[(0,s.jsx)("div",{className:"flex-1 overflow-auto py-responsive",children:(0,s.jsxs)("nav",{className:"px-responsive space-y-3",children:[(0,s.jsx)("div",{className:"px-2 mb-4",children:(0,s.jsx)("h3",{className:"text-2xs font-bold text-muted-foreground uppercase tracking-wider",children:"Navigation"})}),(0,s.jsx)("div",{children:(0,s.jsxs)(n(),{href:"/trade",className:`group flex items-center px-4 py-3.5 text-sm rounded-2xl transition-all duration-300 ${"/trade"===r?"bg-gradient-to-r from-primary via-primary/95 to-primary/80 text-primary-foreground font-semibold shadow-xl shadow-primary/30 scale-105":"text-foreground hover:bg-gradient-to-r hover:from-muted/60 hover:to-muted/40 hover:shadow-lg hover:scale-102"}`,onClick:y,children:[(0,s.jsx)("div",{className:`mr-3 p-1.5 rounded-lg transition-all duration-300 ${"/trade"===r?"bg-primary-foreground/20":"group-hover:bg-primary/10"}`,children:(0,s.jsx)(l.A,{className:"h-4 w-4"})}),(0,s.jsx)("span",{className:"font-medium",children:"Trade"}),"/trade"===r&&(0,s.jsx)("div",{className:"ml-auto w-2 h-2 bg-primary-foreground rounded-full animate-pulse"})]})}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("button",{className:"group w-full flex items-center justify-between px-4 py-3.5 text-sm rounded-2xl text-foreground hover:bg-gradient-to-r hover:from-muted/60 hover:to-muted/40 hover:shadow-lg transition-all duration-300",onClick:()=>f("trading"),children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"mr-3 p-1.5 rounded-lg group-hover:bg-primary/10 transition-all duration-300",children:(0,s.jsx)(d.A,{className:"h-4 w-4"})}),(0,s.jsx)("span",{className:"font-medium",children:"Trading"})]}),"trading"===o?(0,s.jsx)(c.A,{className:"h-4 w-4 text-muted-foreground transition-transform duration-300 group-hover:text-foreground"}):(0,s.jsx)(u.A,{className:"h-4 w-4 text-muted-foreground transition-transform duration-300 group-hover:text-foreground"})]}),"trading"===o&&(0,s.jsxs)("div",{className:"mt-3 ml-8 space-y-2 animate-in slide-in-from-top-3 duration-300",children:[(0,s.jsxs)(n(),{href:"/trade",className:`group flex items-center justify-between px-4 py-3 text-sm rounded-xl transition-all duration-300 ${"/trade"===r?"bg-gradient-to-r from-primary/30 via-primary/25 to-primary/20 text-primary font-bold border border-primary/40 shadow-lg shadow-primary/20 scale-105":"text-foreground hover:text-primary hover:bg-gradient-to-r hover:from-primary/10 hover:to-primary/5 hover:border hover:border-primary/20 hover:shadow-md hover:scale-102 font-semibold"}`,onClick:y,children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:`mr-3 p-1.5 rounded-lg transition-all duration-300 ${"/trade"===r?"bg-primary/20 text-primary":"bg-primary/10 text-primary group-hover:bg-primary/15"}`,children:(0,s.jsx)(d.A,{className:"h-3.5 w-3.5"})}),(0,s.jsx)("span",{children:"Futures"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:["/trade"===r&&(0,s.jsx)("div",{className:"w-2 h-2 bg-primary rounded-full animate-pulse"}),(0,s.jsx)("span",{className:"text-xs bg-gradient-to-r from-emerald-500/20 to-emerald-600/20 text-emerald-600 dark:text-emerald-400 px-2.5 py-1 rounded-lg font-bold border border-emerald-500/30 shadow-sm",children:"LIVE"})]})]}),(0,s.jsxs)(n(),{href:"#",className:"group flex items-center justify-between px-4 py-2.5 text-sm rounded-xl text-muted-foreground hover:text-foreground hover:bg-gradient-to-r hover:from-muted/40 hover:to-muted/20 transition-all duration-300",onClick:y,children:[(0,s.jsx)("span",{children:"Margin"}),(0,s.jsx)("span",{className:"text-xs bg-gradient-to-r from-amber-500/20 to-amber-600/20 text-amber-600 dark:text-amber-400 px-2.5 py-1 rounded-lg font-medium border border-amber-500/20",children:"Soon"})]}),(0,s.jsxs)(n(),{href:"#",className:"group flex items-center justify-between px-4 py-2.5 text-sm rounded-xl text-muted-foreground hover:text-foreground hover:bg-gradient-to-r hover:from-muted/40 hover:to-muted/20 transition-all duration-300",onClick:y,children:[(0,s.jsx)("span",{children:"Options"}),(0,s.jsx)("span",{className:"text-xs bg-gradient-to-r from-amber-500/20 to-amber-600/20 text-amber-600 dark:text-amber-400 px-2.5 py-1 rounded-lg font-medium border border-amber-500/20",children:"Soon"})]})]})]}),(0,s.jsx)("div",{children:(0,s.jsxs)(n(),{href:"/wallet",className:`group flex items-center px-4 py-3.5 text-sm rounded-2xl transition-all duration-300 ${"/wallet"===r?"bg-gradient-to-r from-primary via-primary/95 to-primary/80 text-primary-foreground font-semibold shadow-xl shadow-primary/30 scale-105":"text-foreground hover:bg-gradient-to-r hover:from-muted/60 hover:to-muted/40 hover:shadow-lg hover:scale-102"}`,onClick:y,children:[(0,s.jsx)("div",{className:`mr-3 p-1.5 rounded-lg transition-all duration-300 ${"/wallet"===r?"bg-primary-foreground/20":"group-hover:bg-primary/10"}`,children:(0,s.jsx)(m.A,{className:"h-4 w-4"})}),(0,s.jsx)("span",{className:"font-medium",children:"Wallet"}),"/wallet"===r&&(0,s.jsx)("div",{className:"ml-auto w-2 h-2 bg-primary-foreground rounded-full animate-pulse"})]})}),(0,s.jsx)("div",{children:(0,s.jsxs)(n(),{href:"/statistics",className:`group flex items-center px-4 py-3.5 text-sm rounded-2xl transition-all duration-300 ${"/statistics"===r?"bg-gradient-to-r from-primary via-primary/95 to-primary/80 text-primary-foreground font-semibold shadow-xl shadow-primary/30 scale-105":"text-foreground hover:bg-gradient-to-r hover:from-muted/60 hover:to-muted/40 hover:shadow-lg hover:scale-102"}`,onClick:y,children:[(0,s.jsx)("div",{className:`mr-3 p-1.5 rounded-lg transition-all duration-300 ${"/statistics"===r?"bg-primary-foreground/20":"group-hover:bg-primary/10"}`,children:(0,s.jsx)(h.A,{className:"h-4 w-4"})}),(0,s.jsx)("span",{className:"font-medium",children:"Statistics"}),"/statistics"===r&&(0,s.jsx)("div",{className:"ml-auto w-2 h-2 bg-primary-foreground rounded-full animate-pulse"})]})}),(0,s.jsx)("div",{children:(0,s.jsxs)(n(),{href:"/chat",className:`group flex items-center px-4 py-3.5 text-sm rounded-2xl transition-all duration-300 ${"/chat"===r?"bg-gradient-to-r from-primary via-primary/95 to-primary/80 text-primary-foreground font-semibold shadow-xl shadow-primary/30 scale-105":"text-foreground hover:bg-gradient-to-r hover:from-muted/60 hover:to-muted/40 hover:shadow-lg hover:scale-102"}`,onClick:y,children:[(0,s.jsx)("div",{className:`mr-3 p-1.5 rounded-lg transition-all duration-300 ${"/chat"===r?"bg-primary-foreground/20":"group-hover:bg-primary/10"}`,children:(0,s.jsx)(b.A,{className:"h-4 w-4"})}),(0,s.jsx)("span",{className:"font-medium",children:"AI Chat"}),"/chat"===r&&(0,s.jsx)("div",{className:"ml-auto w-2 h-2 bg-primary-foreground rounded-full animate-pulse"})]})}),(0,s.jsx)("div",{children:(0,s.jsxs)(n(),{href:"/settings",className:`group flex items-center px-4 py-3.5 text-sm rounded-2xl transition-all duration-300 ${"/settings"===r?"bg-gradient-to-r from-primary via-primary/95 to-primary/80 text-primary-foreground font-semibold shadow-xl shadow-primary/30 scale-105":"text-foreground hover:bg-gradient-to-r hover:from-muted/60 hover:to-muted/40 hover:shadow-lg hover:scale-102"}`,onClick:y,children:[(0,s.jsx)("div",{className:`mr-3 p-1.5 rounded-lg transition-all duration-300 ${"/settings"===r?"bg-primary-foreground/20":"group-hover:bg-primary/10"}`,children:(0,s.jsx)(p.A,{className:"h-4 w-4"})}),(0,s.jsx)("span",{className:"font-medium",children:"Settings"}),"/settings"===r&&(0,s.jsx)("div",{className:"ml-auto w-2 h-2 bg-primary-foreground rounded-full animate-pulse"})]})})]})}),(0,s.jsx)("div",{className:"p-responsive border-t border-border/30 bg-gradient-to-br from-muted/30 via-muted/20 to-background/50 backdrop-blur-sm",children:(0,s.jsxs)("div",{className:"space-y-3 sm:space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-2xl bg-gradient-to-r from-background/60 to-muted/40 border border-border/30",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-primary rounded-full animate-pulse"}),(0,s.jsx)("span",{className:"text-responsive-xs font-semibold text-foreground",children:"Theme"})]}),(0,s.jsx)(v,{})]}),(0,s.jsx)("div",{className:"flex items-center justify-center",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("span",{className:"bg-gradient-to-r from-primary/25 via-primary/20 to-primary/15 text-primary px-3 py-1.5 sm:px-4 sm:py-2 rounded-2xl text-2xs sm:text-xs font-bold border border-primary/30 shadow-lg shadow-primary/10",children:"BETA VERSION"}),(0,s.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-emerald-400 to-emerald-500 rounded-full border-2 border-background animate-bounce"})]})}),(0,s.jsxs)("button",{onClick:async()=>{try{let{signOutUser:e}=await Promise.resolve().then(t.bind(t,95216));await e(),localStorage.removeItem("rememberedUsername"),window.location.href="/login"}catch(e){console.error("Logout error:",e),window.location.href="/login"}},className:"touch-target group flex items-center justify-center px-4 py-3.5 text-responsive-xs rounded-2xl bg-gradient-to-r from-red-500/15 via-red-500/10 to-red-600/15 text-red-600 dark:text-red-400 hover:from-red-500/25 hover:via-red-500/20 hover:to-red-600/25 border border-red-500/25 hover:border-red-500/40 transition-all duration-300 w-full shadow-lg hover:shadow-xl hover:scale-105",children:[(0,s.jsx)("div",{className:"mr-3 p-1 rounded-lg bg-red-500/20 group-hover:bg-red-500/30 transition-all duration-300",children:(0,s.jsx)(x.A,{className:"h-4 w-4 group-hover:scale-110 transition-transform duration-300"})}),(0,s.jsx)("span",{className:"font-semibold",children:"Logout"})]})]})})]})}var j=t(93613),N=t(86356),U=t(85778),P=t(65668),A=t(58209),k=t(6955);function S(){let[e,r]=(0,a.useState)(!1),[t,o]=(0,a.useState)(null),[n,l]=(0,a.useState)(!0),[d,c]=(0,a.useState)(null),u=(0,a.useRef)(null),h=(0,i.useRouter)(),{user:b,signOut:g}=(0,k.A)(),{accountInfo:f,getTotalPnL:y,getAvailableBalance:v}=(0,A.fx)(),w=(()=>{let e=t?.balance?.current||1e4,r=f?.totalWalletBalance||1e4,s=e||r||1e4,a=v();if(null==a||0===a){let e=y()||0;a=Math.max(0,s-(f?.totalPositionInitialMargin||0)+e)}return{totalBalance:s,availableBalance:a,totalPnL:y()||0,currency:t?.balance?.currency||"USDT"}})(),S=async()=>{try{await g(),h.push("/login")}catch(e){console.error("Sign out error:",e)}},D=(e="sm")=>n?(0,s.jsx)("div",{className:`${"sm"===e?"w-10 h-10":"w-16 h-16"} bg-muted rounded-full animate-pulse`}):(t?.avatar,(0,s.jsx)("div",{className:`${{sm:"w-10 h-10 text-sm",lg:"w-16 h-16 text-xl"}["sm"===e?"sm":"lg"]} rounded-full bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center shadow-md border-2 border-white dark:border-gray-700 transition-all duration-200 hover:shadow-lg`,children:(0,s.jsx)("span",{className:"select-none",children:"\uD83D\uDC02"})}));return(0,s.jsxs)("div",{className:"relative",ref:u,children:[(0,s.jsx)("button",{className:`rounded-full transition-all duration-200 ${e?"ring-2 ring-primary/30":"hover:scale-105"} ${n?"opacity-75":""}`,onClick:()=>r(!e),disabled:n,children:D("sm")}),e&&(0,s.jsxs)("div",{className:"absolute right-0 mt-2 w-56 rounded-md shadow-xl bg-card ring-1 ring-black/5 dark:ring-white/10 z-[100] animate-in fade-in slide-in-from-top-5 duration-200",children:[(0,s.jsxs)("div",{className:"py-2 px-3 border-b border-border",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[D("xl"),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("p",{className:"text-sm font-medium",children:t?.name||b?.displayName||"User"}),t?.subscription?.type&&"free"!==t.subscription.type&&(0,s.jsx)("span",{className:"text-xs bg-primary/20 text-primary px-1.5 py-0.5 rounded-md uppercase",children:t.subscription.type})]}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:t?.email||b?.email||"<EMAIL>"})]})]}),d&&(0,s.jsx)("div",{className:"mt-3 pt-2 border-t border-border/50",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-red-500",children:[(0,s.jsx)(j.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"text-xs",children:d})]})}),!d&&(0,s.jsxs)("div",{className:"mt-3 pt-2 border-t border-border/50",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,s.jsx)("span",{className:"text-xs text-muted-foreground",children:"Total Balance"}),n?(0,s.jsx)("div",{className:"h-4 w-16 bg-muted rounded animate-pulse"}):(0,s.jsxs)("span",{className:"text-sm font-medium",children:[w.totalBalance.toFixed(2)," ",w.currency]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,s.jsx)("span",{className:"text-xs text-muted-foreground",children:"Available"}),n?(0,s.jsx)("div",{className:"h-4 w-16 bg-muted rounded animate-pulse"}):(0,s.jsxs)("span",{className:"text-sm font-medium",children:[w.availableBalance.toFixed(2)," ",w.currency]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-xs text-muted-foreground",children:"Unrealized PnL"}),n?(0,s.jsx)("div",{className:"h-4 w-16 bg-muted rounded animate-pulse"}):(0,s.jsxs)("span",{className:`text-sm font-medium ${w.totalPnL>=0?"text-emerald-500":"text-red-500"}`,children:[w.totalPnL>=0?"+":"",w.totalPnL.toFixed(2)," ",w.currency]})]}),t?.balance?.lastUpdated&&(0,s.jsx)("div",{className:"mt-2 pt-1 border-t border-border/30",children:(0,s.jsxs)("span",{className:"text-xs text-muted-foreground",children:["Updated: ",new Date(t.balance.lastUpdated).toLocaleTimeString()]})})]})]}),(0,s.jsxs)("div",{className:"py-1",children:[(0,s.jsxs)("button",{className:"flex items-center w-full px-4 py-2 text-sm text-foreground hover:bg-muted transition-colors duration-150",onClick:()=>{r(!1),h.push("/settings")},children:[(0,s.jsx)(N.A,{className:"h-4 w-4 mr-3 text-muted-foreground"}),"My Profile"]}),(0,s.jsxs)("button",{className:"flex items-center w-full px-4 py-2 text-sm text-foreground hover:bg-muted transition-colors duration-150",onClick:()=>{r(!1),h.push("/wallet")},children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-3 text-muted-foreground"}),"Wallet"]}),(0,s.jsxs)("button",{className:"flex items-center w-full px-4 py-2 text-sm text-foreground hover:bg-muted transition-colors duration-150",onClick:()=>{r(!1),h.push("/settings?tab=payment")},children:[(0,s.jsx)(U.A,{className:"h-4 w-4 mr-3 text-muted-foreground"}),"Billing"]}),(0,s.jsxs)("button",{className:"flex items-center w-full px-4 py-2 text-sm text-foreground hover:bg-muted transition-colors duration-150",onClick:()=>{r(!1),h.push("/settings")},children:[(0,s.jsx)(p.A,{className:"h-4 w-4 mr-3 text-muted-foreground"}),"Settings"]}),(0,s.jsxs)("button",{className:"flex items-center w-full px-4 py-2 text-sm text-foreground hover:bg-muted transition-colors duration-150",onClick:()=>{r(!1),window.open("mailto:<EMAIL>","_blank")},children:[(0,s.jsx)(P.A,{className:"h-4 w-4 mr-3 text-muted-foreground"}),"Help Center"]})]}),(0,s.jsx)("div",{className:"py-1 border-t border-border",children:(0,s.jsxs)("button",{className:"flex items-center w-full px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-muted transition-colors duration-150",onClick:S,children:[(0,s.jsx)(x.A,{className:"h-4 w-4 mr-3"}),"Sign out"]})})]})]})}t(21643);var D=t(11860),T=t(12941);function I({onToggle:e,isOpen:r}){return(0,s.jsx)("button",{className:"lg:hidden touch-target rounded-md hover:bg-muted transition-colors",onClick:()=>e(!r),"aria-label":r?"Close sidebar":"Open sidebar",children:r?(0,s.jsx)(D.A,{className:"h-6 w-6 text-foreground"}):(0,s.jsx)(T.A,{className:"h-6 w-6 text-foreground"})})}var O=t(25541),E=t(5336),C=t(43649),L=t(96882),M=t(97051),_=t(13964),B=t(70695),F=t(26512);function $({isOpen:e,onToggle:r,onClose:t}){let{user:i}=(0,k.A)(),[o,n]=(0,a.useState)([]),[l,d]=(0,a.useState)(0),c=(0,a.useRef)(null),u=async e=>{await B.A.markAsRead(e)},m=async()=>{await B.A.markAllAsRead()},h=e=>{switch(e){case"trade":return(0,s.jsx)(O.A,{className:"h-4 w-4 text-blue-500"});case"success":return(0,s.jsx)(E.A,{className:"h-4 w-4 text-green-500"});case"warning":return(0,s.jsx)(C.A,{className:"h-4 w-4 text-yellow-500"});case"error":return(0,s.jsx)(C.A,{className:"h-4 w-4 text-red-500"});default:return(0,s.jsx)(L.A,{className:"h-4 w-4 text-blue-500"})}},b=e=>{if(!e)return"Just now";try{let r=e.toDate?e.toDate():new Date(e);return(0,F.m)(r,{addSuffix:!0})}catch(e){return"Just now"}};return(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)("button",{onClick:r,className:"touch-target relative text-muted-foreground hover:text-foreground transition-colors","aria-label":"Notifications",children:[(0,s.jsx)(M.A,{className:"h-5 w-5"}),l>0&&(0,s.jsx)("span",{className:"absolute -top-0.5 -right-0.5 bg-primary text-primary-foreground text-2xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse font-semibold",children:l>99?"99+":l})]}),e&&(0,s.jsxs)("div",{ref:c,className:"absolute right-0 mt-2 w-80 max-w-[calc(100vw-2rem)] bg-card rounded-lg shadow-xl border border-border z-[100] animate-in fade-in slide-in-from-top-5 duration-200",children:[(0,s.jsx)("div",{className:"p-3 border-b border-border",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h3",{className:"font-semibold text-responsive-sm",children:"Notifications"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[l>0&&(0,s.jsx)("button",{onClick:m,className:"text-2xs text-primary hover:text-primary/90 cursor-pointer font-medium",children:"Mark all as read"}),(0,s.jsx)("button",{onClick:t,className:"text-muted-foreground hover:text-foreground p-1 rounded-md hover:bg-muted transition-colors",children:(0,s.jsx)(D.A,{className:"h-4 w-4"})})]})]})}),(0,s.jsx)("div",{className:"max-h-[400px] overflow-y-auto",children:0===o.length?(0,s.jsxs)("div",{className:"p-6 text-center",children:[(0,s.jsx)(M.A,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),(0,s.jsx)("p",{className:"text-responsive-xs text-muted-foreground",children:"No notifications yet"}),(0,s.jsx)("p",{className:"text-2xs text-muted-foreground mt-1",children:"You'll see trading updates and system notifications here"})]}):(0,s.jsx)("div",{className:"divide-y divide-border",children:o.slice(0,10).map(e=>(0,s.jsx)("div",{className:`p-3 hover:bg-muted/50 transition-colors ${e.read?"":"bg-primary/5 border-l-2 border-l-primary"}`,children:(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)("div",{className:"flex-shrink-0 mt-0.5",children:h(e.type)}),(0,s.jsx)("div",{className:"flex-1 min-w-0",children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("p",{className:"text-responsive-xs font-medium text-foreground",children:e.title}),(0,s.jsx)("p",{className:"text-2xs text-muted-foreground mt-1 line-clamp-2",children:e.message}),(0,s.jsx)("p",{className:"text-2xs text-muted-foreground mt-1",children:b(e.createdAt)})]}),!e.read&&(0,s.jsx)("button",{onClick:()=>u(e.id),className:"ml-2 p-1 text-muted-foreground hover:text-foreground rounded-md hover:bg-muted transition-colors",title:"Mark as read",children:(0,s.jsx)(_.A,{className:"h-3 w-3"})})]})})]})},e.id))})}),o.length>0&&(0,s.jsx)("div",{className:"p-2 border-t border-border text-center",children:(0,s.jsx)("button",{className:"text-responsive-xs text-primary hover:text-primary/90 font-medium",children:"View all notifications"})})]})]})}function z({children:e}){(0,i.useRouter)();let[r,t]=(0,a.useState)(!0),[o,n]=(0,a.useState)(!1),[l,d]=(0,a.useState)(!1),[c,u]=(0,a.useState)(!0),[m,h]=(0,a.useState)(!1),{user:b,loading:p}=(0,k.A)();return r||p?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("svg",{className:"animate-spin h-12 w-12 text-primary mx-auto",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,s.jsx)("p",{className:"mt-4 text-lg font-medium text-foreground",children:"Loading..."})]})}):(0,s.jsx)(A.rM,{children:(0,s.jsx)("div",{className:"min-h-screen w-full bg-background text-foreground",children:(0,s.jsx)("div",{className:"h-full w-full",children:(0,s.jsxs)("div",{className:"bg-card shadow-lg h-full",children:[(0,s.jsxs)("header",{className:"relative flex items-center justify-between px-4 py-2 border-b border-border bg-card/50 backdrop-blur-sm z-50",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)("div",{className:"lg:hidden",children:(0,s.jsx)(I,{onToggle:n,isOpen:o})}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("h1",{className:"text-primary font-bold text-xl tracking-tight bg-gradient-to-r from-primary via-primary/90 to-primary/70 bg-clip-text text-transparent",children:"ThePaperBull"}),(0,s.jsx)("span",{className:"ml-2 text-xs bg-gradient-to-r from-primary/20 to-primary/30 text-primary px-2 py-0.5 rounded-full font-semibold border border-primary/20",children:"BETA"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3 sm:space-x-5 relative z-50",children:[(0,s.jsx)($,{isOpen:l,onToggle:()=>d(!l),onClose:()=>d(!1)}),(0,s.jsx)(S,{})]})]}),(0,s.jsxs)("div",{className:"relative h-[calc(100vh-60px)]",children:[o&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-20 md:hidden"}),(0,s.jsx)("div",{id:"sidebar",className:`${o?"translate-x-0":"-translate-x-full"} fixed inset-y-0 left-0 z-30 w-80 transform transition-all duration-300 ease-in-out md:translate-x-0 shadow-xl`,style:{top:"60px",height:"calc(100vh - 60px)",transform:m&&window.innerWidth>=768?`translateX(${c?"0":"-100%"})`:o?"translateX(0)":"translateX(-100%)"},children:(0,s.jsx)(w,{onCloseMobile:()=>n(!1)})}),(0,s.jsx)("div",{className:"hidden md:flex fixed left-0 top-1/2 transform -translate-y-1/2 z-40",children:(0,s.jsx)("button",{onClick:()=>u(!c),className:"bg-gradient-to-r from-primary to-primary/80 text-primary-foreground rounded-r-xl p-3 shadow-lg hover:shadow-xl transition-all duration-300 group",style:{transform:`translateX(${c?"320px":"0px"})`},"aria-label":c?"Hide sidebar":"Show sidebar",children:c?(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"group-hover:scale-110 transition-transform duration-200",children:(0,s.jsx)("path",{d:"m15 18-6-6 6-6"})}):(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"group-hover:scale-110 transition-transform duration-200",children:(0,s.jsx)("path",{d:"m9 18 6-6-6-6"})})})}),(0,s.jsx)("div",{className:"w-full h-full overflow-auto",children:(0,s.jsx)(a.Suspense,{fallback:(0,s.jsx)("div",{children:"Loading..."}),children:e})})]})]})})})})}},56777:(e,r,t)=>{Promise.resolve().then(t.bind(t,50579))},58209:(e,r,t)=>{"use strict";t.d(r,{rM:()=>m,fx:()=>h});var s=t(60687),a=t(43210),i=t(83427),o=t(21643),n=t(70695);class l{constructor(){this.state={positions:[],orders:[],trades:[],marketData:{},accountInfo:null,isLoading:!1,error:null},this.subscribers=[],this.unsubscribePositions=null,this.unsubscribeOrders=null,this.userUnsubscribe=null,this.userServiceUnsubscribe=null,this.orderIdCounter=1,this.positionIdCounter=1,this.tradeIdCounter=1,this.convertUserPositionToPosition=e=>({id:e.id,symbol:e.symbol,side:e.side,entryPrice:e.entryPrice,markPrice:e.markPrice,size:e.size,margin:e.margin,leverage:e.leverage,pnl:e.pnl,pnlPercent:e.pnlPercent,liquidationPrice:e.liquidationPrice,stopLoss:e.stopLoss,takeProfit:e.takeProfit,timestamp:e.createdAt?.toMillis?.()||Date.now(),orderId:e.orderId}),this.convertUserOrderToOrder=e=>({id:e.id,symbol:e.symbol,side:e.side,type:e.type,price:e.price,origQty:e.origQty,executedQty:e.executedQty,status:e.status,leverage:e.leverage,stopLoss:e.stopLoss,takeProfit:e.takeProfit,timestamp:e.createdAt?.toMillis?.()||Date.now()}),this.convertUserTradeToTrade=e=>({id:e.id,symbol:e.symbol,side:e.side,price:e.price,quantity:e.quantity,commission:e.commission,realizedPnl:e.realizedPnl,leverage:e.leverage,timestamp:e.createdAt?.toMillis?.()||Date.now(),orderId:e.orderId,positionId:e.positionId}),this.initializeUserSubscription()}subscribe(e){return this.subscribers.push(e),()=>{let r=this.subscribers.indexOf(e);r>-1&&this.subscribers.splice(r,1)}}notifySubscribers(){this.subscribers.forEach(e=>e(this.state))}initializeUserSubscription(){this.userUnsubscribe=o.A.subscribe(e=>{e?(this.loadUserTradingData(e.id),this.initializeAccountData()):this.clearTradingData()})}async loadUserTradingData(e){try{this.state.isLoading=!0,this.notifySubscribers(),this.unsubscribePositions&&this.unsubscribePositions();try{this.unsubscribePositions=i.b.subscribeToUserPositions(e,e=>{this.state.positions=e.map(this.convertUserPositionToPosition),this.updateAccountInfo(),this.notifySubscribers()})}catch(e){console.error("Error subscribing to positions:",e),this.state.positions=[]}this.unsubscribeOrders&&this.unsubscribeOrders();try{this.unsubscribeOrders=i.b.subscribeToUserOrders(e,e=>{this.state.orders=e.map(this.convertUserOrderToOrder),this.updateAccountInfo(),this.notifySubscribers()})}catch(e){console.error("Error subscribing to orders:",e),this.state.orders=[]}try{let r=await i.b.getUserTrades(e);this.state.trades=r.map(this.convertUserTradeToTrade)}catch(e){console.error("Error loading trades:",e),this.state.trades=[]}this.state.isLoading=!1,this.state.error=null,this.notifySubscribers()}catch(e){console.error("Error loading user trading data:",e),this.state.isLoading=!1,"permission-denied"===e.code?this.state.error="Trading data access requires proper authentication. Please sign in again.":this.state.error="Failed to load trading data. Please try again.",this.state.positions=[],this.state.orders=[],this.state.trades=[],this.notifySubscribers()}}clearTradingData(){this.state.positions=[],this.state.orders=[],this.state.trades=[],this.state.accountInfo=null,this.unsubscribePositions&&(this.unsubscribePositions(),this.unsubscribePositions=null),this.unsubscribeOrders&&(this.unsubscribeOrders(),this.unsubscribeOrders=null),this.userServiceUnsubscribe&&(this.userServiceUnsubscribe(),this.userServiceUnsubscribe=null),this.notifySubscribers()}initializeAccountData(){this.userServiceUnsubscribe=o.A.subscribe(e=>{e&&this.syncAccountWithUserData(e)}),o.A.getUserBalance(),this.syncAccountWithUserData(o.A.getUser())}syncAccountWithUserData(e){let r=e?.balance?.current||o.A.getUserBalance()||1e4,t=this.state.accountInfo,s=t?.totalUnrealizedProfit||0,a=t?.totalPositionInitialMargin||0,i=t?.totalOpenOrderInitialMargin||0,n=r-a-i+s;this.state.accountInfo={totalWalletBalance:r,totalUnrealizedProfit:s,totalMarginBalance:r+s,totalPositionInitialMargin:a,totalOpenOrderInitialMargin:i,availableBalance:Math.max(0,n),maxWithdrawAmount:Math.max(0,n),balances:[{asset:"USDT",free:r,locked:0,total:r}],canTrade:!0,canDeposit:!0,canWithdraw:!0,updateTime:Date.now()},this.notifySubscribers()}updateAccountInfo(){if(!this.state.accountInfo)return;let e=this.state.positions.reduce((e,r)=>e+r.pnl,0),r=this.state.positions.reduce((e,r)=>e+r.margin,0),t=this.state.orders.filter(e=>"NEW"===e.status).reduce((e,r)=>e+r.origQty*r.price/(r.leverage||10),0);this.state.accountInfo.totalUnrealizedProfit=e,this.state.accountInfo.totalPositionInitialMargin=r,this.state.accountInfo.totalOpenOrderInitialMargin=t,this.state.accountInfo.totalMarginBalance=this.state.accountInfo.totalWalletBalance+e;let s=this.state.accountInfo.totalWalletBalance-r-t+e;this.state.accountInfo.availableBalance=Math.max(0,s),this.state.accountInfo.maxWithdrawAmount=Math.max(0,s),this.state.accountInfo.updateTime=Date.now();let a=this.state.accountInfo.balances.find(e=>"USDT"===e.asset);a&&(a.free=this.state.accountInfo.availableBalance,a.locked=r+t,a.total=this.state.accountInfo.totalWalletBalance)}getPositions(){return[...this.state.positions]}getOrders(){return[...this.state.orders]}getTrades(){return[...this.state.trades]}getMarketData(e){return this.state.marketData[e]||null}getAccountInfo(){return this.state.accountInfo}updateMarketData(e,r){this.state.marketData[e]=r,this.state.positions=this.state.positions.map(t=>t.symbol===e?this.updatePositionPnL(t,r.price):t),this.updateAccountInfo(),this.notifySubscribers()}calculatePnL(e,r){let t=("LONG"===e.side?r-e.entryPrice:e.entryPrice-r)*e.size,s=t/e.margin*100;return{pnl:t,pnlPercent:s}}updatePositionPnL(e,r){let{pnl:t,pnlPercent:s}=this.calculatePnL(e,r);return{...e,markPrice:r,pnl:t,pnlPercent:s}}async placeOrder(e){let r=o.A.getFirebaseUser();if(console.log("Authentication check:",{hasUser:!!r,userId:r?.uid,userEmail:r?.email,userServiceUser:o.A.getUser()}),!r)throw console.error("Order placement failed: User not authenticated"),Error("User not authenticated. Please sign in and try again.");try{await r.getIdToken(!0),console.log("User token refreshed successfully")}catch(e){throw console.error("Token refresh failed:",e),Error("Authentication token expired. Please sign in again.")}if(!e.symbol||!e.side||!e.type||!e.quantity)throw console.error("Order placement failed: Invalid order parameters",e),Error("Invalid order parameters. Please check your order details.");if(e.quantity<=0)throw console.error("Order placement failed: Invalid quantity",e.quantity),Error("Order quantity must be greater than 0.");try{this.state.isLoading=!0,this.notifySubscribers(),console.log("Placing order:",e);let t=this.getMarketData(e.symbol)?.price;!t&&e.price&&(t=e.price),t||(t=({BTCUSDT:43e3,ETHUSDT:2500,XRPUSDT:.6,SOLUSDT:100,BNBUSDT:300,DOGEUSDT:.08,ADAUSDT:.5,TRXUSDT:.1})[e.symbol]||100,console.warn(`Using fallback price for ${e.symbol}: ${t}`));let s=o.A.getUserBalance(),a=e.quantity*t,l=a/(e.leverage||10);if(l>s)throw console.error("Order placement failed: Insufficient balance",{requiredMargin:l,userBalance:s,orderValue:a}),Error(`Insufficient balance. Required: ${l.toFixed(2)} USDT, Available: ${s.toFixed(2)} USDT`);let d={symbol:e.symbol,side:e.side,type:e.type,price:e.price||t,origQty:e.quantity,executedQty:0,status:"NEW",leverage:e.leverage||10,...e.stopLoss&&{stopLoss:e.stopLoss},...e.takeProfit&&{takeProfit:e.takeProfit}};console.log("Order data prepared:",d);let c=await i.b.addOrder(r.uid,d);return console.log("Order added to Firestore with ID:",c),"MARKET"===e.type?(console.log("Executing market order immediately"),await this.executeOrder(c,d,t),await n.A.createTradeNotification(r.uid,"order_filled",{symbol:e.symbol,side:e.side,price:t,quantity:e.quantity})):console.log("Limit order placed, waiting for execution"),this.state.isLoading=!1,this.state.error=null,this.notifySubscribers(),console.log("Order placement successful:",c),c}catch(e){throw console.error("Order placement error:",e),this.state.isLoading=!1,this.state.error=e instanceof Error?e.message:"Failed to place order",this.notifySubscribers(),e}}async executeOrder(e,r,t){let s=o.A.getFirebaseUser();if(!s)throw console.error("Cannot execute order: User not authenticated"),Error("User not authenticated");try{console.log("Executing order:",{orderId:e,orderData:r,executionPrice:t});let a=r.origQty*t*.001;console.log("Calculated commission:",a);let l={symbol:r.symbol,side:"BUY"===r.side?"LONG":"SHORT",entryPrice:t,markPrice:t,size:r.origQty,margin:r.origQty*t/r.leverage,leverage:r.leverage,pnl:0,pnlPercent:0,liquidationPrice:this.calculateLiquidationPrice(t,"BUY"===r.side?"LONG":"SHORT",r.leverage),orderId:e,...r.stopLoss&&{stopLoss:r.stopLoss},...r.takeProfit&&{takeProfit:r.takeProfit}};console.log("Creating position:",l);let d=await i.b.addPosition(s.uid,l);console.log("Position created with ID:",d),await n.A.createTradeNotification(s.uid,"position_opened",{symbol:r.symbol,side:l.side,size:r.origQty,entryPrice:t});let c={symbol:r.symbol,side:r.side,price:t,quantity:r.origQty,commission:a,realizedPnl:0,leverage:r.leverage,orderId:e};console.log("Creating trade record:",c);let u=await i.b.addTrade(s.uid,c);if(console.log("Trade created with ID:",u),console.log("Updating order status to FILLED"),await i.b.updateOrder(e,{status:"FILLED",executedQty:r.origQty}),a>0){console.log("Updating user balance for commission");let e=o.A.getUserBalance();await o.A.updateBalance(e-a,"commission",`Trading commission: ${a.toFixed(2)} USDT`),console.log("Balance updated, commission deducted:",a)}console.log("Order execution completed successfully")}catch(r){console.error("Error executing order:",r);try{await i.b.updateOrder(e,{status:"FAILED"})}catch(e){console.error("Failed to update order status to FAILED:",e)}throw r}}calculateLiquidationPrice(e,r,t){let s=.995-1/t;return"LONG"===r?e*s:e/s}async cancelOrder(e){try{await i.b.updateOrder(e,{status:"CANCELLED"})}catch(e){throw console.error("Error cancelling order:",e),e}}async closePosition(e){let r=o.A.getFirebaseUser();if(r)try{let t=this.state.positions.find(r=>r.id===e);if(!t)return;let s=this.getMarketData(t.symbol)?.price||t.markPrice,a={symbol:t.symbol,side:"LONG"===t.side?"SELL":"BUY",price:s,quantity:t.size,commission:t.size*s*.001,realizedPnl:t.pnl,leverage:t.leverage,orderId:t.orderId||"",positionId:e};await i.b.addTrade(r.uid,a);let l=o.A.getUserBalance()+t.pnl-a.commission;await o.A.updateBalance(l,t.pnl>0?"trade_profit":"trade_loss",`Position closed: ${t.pnl>0?"+":""}${t.pnl.toFixed(2)} USDT`),await i.b.deletePosition(e),await n.A.createTradeNotification(r.uid,"position_closed",{symbol:t.symbol,side:t.side,pnl:t.pnl,closePrice:s})}catch(e){throw console.error("Error closing position:",e),e}}destroy(){this.unsubscribePositions&&this.unsubscribePositions(),this.unsubscribeOrders&&this.unsubscribeOrders(),this.userUnsubscribe&&this.userUnsubscribe(),this.userServiceUnsubscribe&&this.userServiceUnsubscribe()}}let d=new l;class c{constructor(){this.simulators=new Map,this.intervalId=null,this.subscribers=[],this.initializeSimulators()}initializeSimulators(){[{symbol:"BTCUSDT",basePrice:43200,volatility:.02},{symbol:"ETHUSDT",basePrice:2320,volatility:.025},{symbol:"SOLUSDT",basePrice:142,volatility:.03},{symbol:"ADAUSDT",basePrice:.45,volatility:.035},{symbol:"XRPUSDT",basePrice:.62,volatility:.04},{symbol:"BNBUSDT",basePrice:315,volatility:.025},{symbol:"DOGEUSDT",basePrice:.08,volatility:.05},{symbol:"TRXUSDT",basePrice:.12,volatility:.04},{symbol:"LINKUSDT",basePrice:14.5,volatility:.03},{symbol:"AVAXUSDT",basePrice:38.2,volatility:.035}].forEach(e=>{this.simulators.set(e.symbol,{symbol:e.symbol,basePrice:e.basePrice,volatility:e.volatility,trend:(Math.random()-.5)*.001,lastPrice:e.basePrice,lastUpdate:Date.now()})})}start(){this.intervalId||(this.intervalId=setInterval(()=>{this.updatePrices()},1e3))}stop(){this.intervalId&&(clearInterval(this.intervalId),this.intervalId=null)}updatePrices(){this.simulators.forEach(e=>{let r=Date.now(),t=(r-e.lastUpdate)/1e3,s=(Math.random()-.5)*e.volatility*t,a=e.trend*t,i=e.lastPrice*(1+(s+a)),o=i-e.lastPrice,n=o/e.lastPrice*100;.01>Math.random()&&(e.trend=(Math.random()-.5)*.001),e.lastPrice=i,e.lastUpdate=r;let l={symbol:e.symbol,price:i,priceChange:o,priceChangePercent:n,volume:1e6*Math.random(),timestamp:r};this.notifySubscribers(e.symbol,l)})}subscribe(e){return this.subscribers.push(e),()=>{let r=this.subscribers.indexOf(e);r>-1&&this.subscribers.splice(r,1)}}notifySubscribers(e,r){this.subscribers.forEach(t=>t(e,r))}getCurrentPrice(e){let r=this.simulators.get(e);return r?r.lastPrice:null}addSymbol(e,r,t=.03){this.simulators.has(e)||this.simulators.set(e,{symbol:e,basePrice:r,volatility:t,trend:(Math.random()-.5)*.001,lastPrice:r,lastUpdate:Date.now()})}removeSymbol(e){this.simulators.delete(e)}}new c;let u=(0,a.createContext)(void 0);function m({children:e}){let[r,t]=(0,a.useState)({positions:[],orders:[],trades:[],marketData:{},accountInfo:null,isLoading:!1,error:null}),i=(0,a.useCallback)(async e=>{try{return await d.placeOrder(e)}catch(e){throw console.error("Failed to place order:",e),e}},[]),o=(0,a.useCallback)(async e=>{try{return await d.cancelOrder(e),!0}catch(e){throw console.error("Failed to cancel order:",e),e}},[]),n=(0,a.useCallback)(async e=>{try{return await d.closePosition(e),!0}catch(e){throw console.error("Failed to close position:",e),e}},[]),l=(0,a.useCallback)(async e=>{try{return console.log("Position update not implemented in Firebase service yet:",e),!0}catch(e){throw console.error("Failed to update position:",e),e}},[]),c=(0,a.useCallback)((e,r)=>{d.updateMarketData(e,r)},[]),m=(0,a.useCallback)(e=>{console.log("Account info update not needed with Firebase service:",e)},[]),h=(0,a.useCallback)(()=>{t(e=>({...e,error:null}))},[]),b=(0,a.useCallback)(e=>d.getMarketData(e),[]),p=(0,a.useCallback)(e=>r.positions.find(r=>r.symbol===e)||null,[r.positions]),x=(0,a.useCallback)(()=>d.getAccountInfo(),[]),g=(0,a.useCallback)(()=>r.positions.reduce((e,r)=>e+r.pnl,0),[r.positions]),f=(0,a.useCallback)(()=>r.positions.reduce((e,r)=>e+r.margin,0),[r.positions]),y=(0,a.useCallback)(()=>r.accountInfo?.availableBalance||0,[r.accountInfo]),v={positions:r.positions,orders:r.orders,trades:r.trades,marketData:r.marketData,accountInfo:r.accountInfo,isLoading:r.isLoading,error:r.error,state:r,placeOrder:i,cancelOrder:o,closePosition:n,updatePosition:l,updateMarketData:c,updateAccountInfo:m,clearError:h,getMarketData:b,getPositionBySymbol:p,getAccountInfo:x,getTotalPnL:g,getTotalMargin:f,getAvailableBalance:y};return(0,s.jsx)(u.Provider,{value:v,children:e})}function h(){let e=(0,a.useContext)(u);if(void 0===e)throw Error("useTrading must be used within a TradingProvider");return e}},70695:(e,r,t)=>{"use strict";t.d(r,{A:()=>o});var s=t(75535),a=t(80043);class i{initialize(e){this.currentUser=e,this.unsubscribeFirestore&&(this.unsubscribeFirestore(),this.unsubscribeFirestore=null),e?this.subscribeToUserNotifications(e.uid):(this.notifications=[],this.notifySubscribers())}subscribeToUserNotifications(e){let r=(0,s.rJ)(a.db,"notifications"),t=(0,s.P)(r,(0,s._M)("userId","==",e),(0,s.My)("createdAt","desc"));this.unsubscribeFirestore=(0,s.aQ)(t,e=>{this.notifications=e.docs.map(e=>({id:e.id,...e.data()})),this.notifySubscribers()},e=>{console.error("Error listening to notifications:",e)})}subscribe(e){return this.subscribers.push(e),e(this.notifications),()=>{let r=this.subscribers.indexOf(e);r>-1&&this.subscribers.splice(r,1)}}notifySubscribers(){this.subscribers.forEach(e=>e(this.notifications))}getNotifications(){return this.notifications}getUnreadCount(){return this.notifications.filter(e=>!e.read).length}async markAsRead(e){if(this.currentUser)try{let r=(0,s.H9)(a.db,"notifications",e);await (0,s.mZ)(r,{read:!0})}catch(e){console.error("Error marking notification as read:",e)}}async markAllAsRead(){if(this.currentUser)try{let e=this.notifications.filter(e=>!e.read).map(e=>this.markAsRead(e.id));await Promise.all(e)}catch(e){console.error("Error marking all notifications as read:",e)}}async createNotification(e,r,t,i="info",o){try{let n=(0,s.rJ)(a.db,"notifications");await (0,s.gS)(n,{userId:e,title:r,message:t,type:i,read:!1,createdAt:(0,s.O5)(),data:o||null})}catch(e){console.error("Error creating notification:",e)}}async createTradeNotification(e,r,t){let s="",a="";switch(r){case"order_filled":s="Order Filled",a=`Your ${t.side} order for ${t.symbol} has been filled at $${t.price}`;break;case"position_opened":s="Position Opened",a=`New ${t.side} position opened for ${t.symbol} - Size: ${t.size}`;break;case"position_closed":s="Position Closed",a=`${t.symbol} position closed - P&L: ${t.pnl>0?"+":""}$${t.pnl.toFixed(2)}`;break;case"stop_loss":s="Stop Loss Triggered",a=`Stop loss triggered for ${t.symbol} at $${t.price}`;break;case"take_profit":s="Take Profit Hit",a=`Take profit reached for ${t.symbol} at $${t.price}`}await this.createNotification(e,s,a,"trade",t)}async createWelcomeNotification(e){await this.createNotification(e,"Welcome to ThePaperBull!","Start your paper trading journey with $10,000 virtual balance. Practice trading without risk!","success")}destroy(){this.unsubscribeFirestore&&this.unsubscribeFirestore(),this.subscribers=[],this.notifications=[]}constructor(){this.subscribers=[],this.unsubscribeFirestore=null,this.currentUser=null,this.notifications=[]}}let o=new i},71934:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\(dashboard)\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\step-by-step\\thepaperbull-144\\app\\(dashboard)\\layout.tsx","default")},75870:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i}),t(37413);var s=t(31658);let a={runtime:"edge",dynamic:"force-static",alt:"Cryptocurrency Trading Dashboard",size:{width:1200,height:630},contentType:"image/png"};async function i(e){let{__metadata_id__:r,...t}=await e.params,i=(0,s.fillMetadataSegment)(".",t,"twitter-image"),{generateImageMetadata:o}=a;function n(e,r){let t={alt:e.alt,type:e.contentType||"image/png",url:i+(r?"/"+r:"")+"?c783d68783a9fc8b"},{size:s}=e;return s&&(t.width=s.width,t.height=s.height),t}return o?(await o({params:t})).map((e,r)=>{let t=(e.id||r)+"";return n(e,t)}):[n(a,"")]}},83427:(e,r,t)=>{"use strict";t.d(r,{b:()=>o});var s=t(75535),a=t(80043);class i{async createUserProfile(e,r,t){let i=(0,s.O5)(),o={email:r,name:t,avatar:"bull-trader",createdAt:i,subscription:{type:"free",status:"active",startDate:i,features:["basic_trading","paper_trading","basic_charts"]},balance:{current:1e4,maximum:5e4,currency:"USDT",lastUpdated:i},settings:{theme:"system",notifications:{email:!0,push:!0,trading_alerts:!0,price_alerts:!0,news_updates:!1},trading:{default_leverage:10,risk_management:!0,auto_close_positions:!1,confirmation_dialogs:!0}}};return await (0,s.BN)((0,s.H9)(a.db,"users",e),o),await this.addTransaction(e,{type:"deposit",amount:1e4,balance_before:0,balance_after:1e4,description:"Initial balance"}),{id:e,...o}}async getUserProfile(e){try{let r=await (0,s.x7)((0,s.H9)(a.db,"users",e));if(r.exists())return{id:e,...r.data()};return null}catch(e){return console.error("Error getting user profile:",e),null}}async updateUserProfile(e,r){try{let t={...r};r.balance&&(t["balance.lastUpdated"]=(0,s.O5)()),await (0,s.mZ)((0,s.H9)(a.db,"users",e),t)}catch(e){throw console.error("Error updating user profile:",e),e}}async updateUserBalance(e,r){try{await (0,s.mZ)((0,s.H9)(a.db,"users",e),{"balance.current":r,"balance.lastUpdated":(0,s.O5)()})}catch(e){throw console.error("Error updating user balance:",e),e}}async addTransaction(e,r){try{let t={...r,userId:e,timestamp:(0,s.O5)()};return(await (0,s.gS)((0,s.rJ)(a.db,"transactions"),t)).id}catch(e){throw console.error("Error adding transaction:",e),e}}async getUserTransactions(e,r=50){try{let t=(0,s.P)((0,s.rJ)(a.db,"transactions"),(0,s._M)("userId","==",e),(0,s.My)("timestamp","desc"));return new Promise((e,a)=>{(0,s.aQ)(t,t=>{let s=t.docs.map(e=>({id:e.id,...e.data()}));e(s.slice(0,r))},a)})}catch(e){return console.error("Error getting user transactions:",e),[]}}async addPosition(e,r){try{console.log("Adding position to Firestore:",{userId:e,position:r});let t=(0,s.O5)(),i={...r,userId:e,createdAt:t,updatedAt:t},o=await (0,s.gS)((0,s.rJ)(a.db,"positions"),i);return console.log("Position added successfully with ID:",o.id),o.id}catch(e){if(console.error("Error adding position to Firestore:",e),"permission-denied"===e.code)throw Error("Permission denied. Please check your authentication and try again.");if("unavailable"===e.code)throw Error("Service temporarily unavailable. Please try again in a moment.");throw Error(`Failed to create position: ${e.message||"Unknown error"}`)}}async updatePosition(e,r){try{await (0,s.mZ)((0,s.H9)(a.db,"positions",e),{...r,updatedAt:(0,s.O5)()})}catch(e){throw console.error("Error updating position:",e),e}}async deletePosition(e){try{await (0,s.kd)((0,s.H9)(a.db,"positions",e))}catch(e){throw console.error("Error deleting position:",e),e}}async getUserPositions(e){try{let r=(0,s.P)((0,s.rJ)(a.db,"positions"),(0,s._M)("userId","==",e),(0,s.My)("createdAt","desc"));return new Promise((e,t)=>{(0,s.aQ)(r,r=>{let t=r.docs.map(e=>({id:e.id,...e.data()}));e(t)},t)})}catch(e){return console.error("Error getting user positions:",e),[]}}async addOrder(e,r){try{console.log("Adding order to Firestore:",{userId:e,order:r});let t=(0,s.O5)(),i={...r,userId:e,createdAt:t,updatedAt:t},o=await (0,s.gS)((0,s.rJ)(a.db,"orders"),i);return console.log("Order added successfully with ID:",o.id),o.id}catch(e){if(console.error("Error adding order to Firestore:",e),"permission-denied"===e.code)throw Error("Permission denied. Please check your authentication and try again.");if("unavailable"===e.code)throw Error("Service temporarily unavailable. Please try again in a moment.");if("failed-precondition"===e.code)throw Error("Database operation failed. Please check your connection and try again.");throw Error(`Failed to place order: ${e.message||"Unknown error"}`)}}async updateOrder(e,r){try{await (0,s.mZ)((0,s.H9)(a.db,"orders",e),{...r,updatedAt:(0,s.O5)()})}catch(e){throw console.error("Error updating order:",e),e}}async deleteOrder(e){try{await (0,s.kd)((0,s.H9)(a.db,"orders",e))}catch(e){throw console.error("Error deleting order:",e),e}}async getUserOrders(e){try{let r=(0,s.P)((0,s.rJ)(a.db,"orders"),(0,s._M)("userId","==",e),(0,s.My)("createdAt","desc"));return new Promise((e,t)=>{(0,s.aQ)(r,r=>{let t=r.docs.map(e=>({id:e.id,...e.data()}));e(t)},t)})}catch(e){return console.error("Error getting user orders:",e),[]}}async addTrade(e,r){try{console.log("Adding trade to Firestore:",{userId:e,trade:r});let t={...r,userId:e,createdAt:(0,s.O5)()},i=await (0,s.gS)((0,s.rJ)(a.db,"trades"),t);return console.log("Trade added successfully with ID:",i.id),i.id}catch(e){if(console.error("Error adding trade to Firestore:",e),"permission-denied"===e.code)throw Error("Permission denied. Please check your authentication and try again.");if("unavailable"===e.code)throw Error("Service temporarily unavailable. Please try again in a moment.");throw Error(`Failed to record trade: ${e.message||"Unknown error"}`)}}async getUserTrades(e,r=100){try{let t=(0,s.P)((0,s.rJ)(a.db,"trades"),(0,s._M)("userId","==",e),(0,s.My)("createdAt","desc"));return new Promise((e,a)=>{(0,s.aQ)(t,t=>{let s=t.docs.map(e=>({id:e.id,...e.data()}));e(s.slice(0,r))},a)})}catch(e){return console.error("Error getting user trades:",e),[]}}subscribeToUserProfile(e,r){return(0,s.aQ)((0,s.H9)(a.db,"users",e),t=>{t.exists()?r({id:e,...t.data()}):r(null)})}subscribeToUserPositions(e,r){let t=(0,s.P)((0,s.rJ)(a.db,"positions"),(0,s._M)("userId","==",e),(0,s.My)("createdAt","desc"));return(0,s.aQ)(t,e=>{r(e.docs.map(e=>({id:e.id,...e.data()})))})}subscribeToUserOrders(e,r){let t=(0,s.P)((0,s.rJ)(a.db,"orders"),(0,s._M)("userId","==",e),(0,s.My)("createdAt","desc"));return(0,s.aQ)(t,e=>{r(e.docs.map(e=>({id:e.id,...e.data()})))})}}let o=new i},91625:(e,r,t)=>{Promise.resolve().then(t.bind(t,71934))}};