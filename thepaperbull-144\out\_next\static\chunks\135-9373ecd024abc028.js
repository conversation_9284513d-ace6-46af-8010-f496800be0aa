"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[135],{34135:(e,t,s)=>{s.d(t,{rM:()=>h,fx:()=>p});var i=s(95155),r=s(12115),a=s(91317),o=s(50475),n=s(27759);class l{subscribe(e){return this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);t>-1&&this.subscribers.splice(t,1)}}notifySubscribers(){this.subscribers.forEach(e=>e(this.state))}initializeUserSubscription(){this.userUnsubscribe=o.A.subscribe(e=>{e?(this.loadUserTradingData(e.id),this.initializeAccountData()):this.clearTradingData()})}async loadUserTradingData(e){try{this.state.isLoading=!0,this.notifySubscribers(),this.unsubscribePositions&&this.unsubscribePositions();try{this.unsubscribePositions=a.b.subscribeToUserPositions(e,e=>{this.state.positions=e.map(this.convertUserPositionToPosition),this.updateAccountInfo(),this.notifySubscribers()})}catch(e){console.error("Error subscribing to positions:",e),this.state.positions=[]}this.unsubscribeOrders&&this.unsubscribeOrders();try{this.unsubscribeOrders=a.b.subscribeToUserOrders(e,e=>{this.state.orders=e.map(this.convertUserOrderToOrder),this.updateAccountInfo(),this.notifySubscribers()})}catch(e){console.error("Error subscribing to orders:",e),this.state.orders=[]}this.unsubscribeTrades&&this.unsubscribeTrades();try{this.unsubscribeTrades=a.b.subscribeToUserTrades(e,e=>{this.state.trades=e.map(this.convertUserTradeToTrade),this.updateAccountInfo(),this.notifySubscribers()})}catch(e){console.error("Error subscribing to trades:",e),this.state.trades=[]}this.state.isLoading=!1,this.state.error=null,this.notifySubscribers()}catch(e){console.error("Error loading user trading data:",e),this.state.isLoading=!1,"permission-denied"===e.code?this.state.error="Trading data access requires proper authentication. Please sign in again.":this.state.error="Failed to load trading data. Please try again.",this.state.positions=[],this.state.orders=[],this.state.trades=[],this.notifySubscribers()}}clearTradingData(){this.state.positions=[],this.state.orders=[],this.state.trades=[],this.state.accountInfo=null,this.unsubscribePositions&&(this.unsubscribePositions(),this.unsubscribePositions=null),this.unsubscribeOrders&&(this.unsubscribeOrders(),this.unsubscribeOrders=null),this.unsubscribeTrades&&(this.unsubscribeTrades(),this.unsubscribeTrades=null),this.userServiceUnsubscribe&&(this.userServiceUnsubscribe(),this.userServiceUnsubscribe=null),this.notifySubscribers()}initializeAccountData(){this.userServiceUnsubscribe=o.A.subscribe(e=>{e&&this.syncAccountWithUserData(e)}),o.A.getUserBalance(),this.syncAccountWithUserData(o.A.getUser())}syncAccountWithUserData(e){var t;let s=(null==e?void 0:null===(t=e.balance)||void 0===t?void 0:t.current)||o.A.getUserBalance()||1e4,i=this.state.accountInfo,r=(null==i?void 0:i.totalUnrealizedProfit)||0,a=(null==i?void 0:i.totalPositionInitialMargin)||0,n=(null==i?void 0:i.totalOpenOrderInitialMargin)||0,l=s-a-n+r;this.state.accountInfo={totalWalletBalance:s,totalUnrealizedProfit:r,totalMarginBalance:s+r,totalPositionInitialMargin:a,totalOpenOrderInitialMargin:n,availableBalance:Math.max(0,l),maxWithdrawAmount:Math.max(0,l),balances:[{asset:"USDT",free:s,locked:0,total:s}],canTrade:!0,canDeposit:!0,canWithdraw:!0,updateTime:Date.now()},this.notifySubscribers()}updateAccountInfo(){if(!this.state.accountInfo)return;let e=this.state.positions.reduce((e,t)=>e+t.pnl,0),t=this.state.positions.reduce((e,t)=>e+t.margin,0),s=this.state.orders.filter(e=>"NEW"===e.status).reduce((e,t)=>e+t.origQty*t.price/(t.leverage||10),0);this.state.accountInfo.totalUnrealizedProfit=e,this.state.accountInfo.totalPositionInitialMargin=t,this.state.accountInfo.totalOpenOrderInitialMargin=s,this.state.accountInfo.totalMarginBalance=this.state.accountInfo.totalWalletBalance+e;let i=this.state.accountInfo.totalWalletBalance-t-s+e;this.state.accountInfo.availableBalance=Math.max(0,i),this.state.accountInfo.maxWithdrawAmount=Math.max(0,i),this.state.accountInfo.updateTime=Date.now();let r=this.state.accountInfo.balances.find(e=>"USDT"===e.asset);r&&(r.free=this.state.accountInfo.availableBalance,r.locked=t+s,r.total=this.state.accountInfo.totalWalletBalance)}getPositions(){return[...this.state.positions]}getOrders(){return[...this.state.orders]}getTrades(){return[...this.state.trades]}getMarketData(e){return this.state.marketData[e]||null}getAccountInfo(){return this.state.accountInfo}updateMarketData(e,t){this.state.marketData[e]=t,this.state.positions=this.state.positions.map(s=>s.symbol===e?this.updatePositionPnL(s,t.price):s),this.updateAccountInfo(),this.notifySubscribers()}calculatePnL(e,t){let s=("LONG"===e.side?t-e.entryPrice:e.entryPrice-t)*e.size,i=s/e.margin*100;return{pnl:s,pnlPercent:i}}updatePositionPnL(e,t){let{pnl:s,pnlPercent:i}=this.calculatePnL(e,t);return{...e,markPrice:t,pnl:s,pnlPercent:i}}async placeOrder(e){let t=o.A.getFirebaseUser();if(console.log("Authentication check:",{hasUser:!!t,userId:null==t?void 0:t.uid,userEmail:null==t?void 0:t.email,userServiceUser:o.A.getUser()}),!t)throw console.error("Order placement failed: User not authenticated"),Error("User not authenticated. Please sign in and try again.");try{await t.getIdToken(!0),console.log("User token refreshed successfully")}catch(e){throw console.error("Token refresh failed:",e),Error("Authentication token expired. Please sign in again.")}if(!e.symbol||!e.side||!e.type||!e.quantity)throw console.error("Order placement failed: Invalid order parameters",e),Error("Invalid order parameters. Please check your order details.");if(e.quantity<=0)throw console.error("Order placement failed: Invalid quantity",e.quantity),Error("Order quantity must be greater than 0.");try{var s;this.state.isLoading=!0,this.notifySubscribers(),console.log("Placing order:",e);let i=null===(s=this.getMarketData(e.symbol))||void 0===s?void 0:s.price;!i&&e.price&&(i=e.price),i||(i=({BTCUSDT:43e3,ETHUSDT:2500,XRPUSDT:.6,SOLUSDT:100,BNBUSDT:300,DOGEUSDT:.08,ADAUSDT:.5,TRXUSDT:.1})[e.symbol]||100,console.warn("Using fallback price for ".concat(e.symbol,": ").concat(i)));let r=o.A.getUserBalance(),l=e.quantity*i,c=l/(e.leverage||10);if(c>r)throw console.error("Order placement failed: Insufficient balance",{requiredMargin:c,userBalance:r,orderValue:l}),Error("Insufficient balance. Required: ".concat(c.toFixed(2)," USDT, Available: ").concat(r.toFixed(2)," USDT"));let d={symbol:e.symbol,side:e.side,type:e.type,price:e.price||i,origQty:e.quantity,executedQty:0,status:"NEW",leverage:e.leverage||10,...e.stopLoss&&{stopLoss:e.stopLoss},...e.takeProfit&&{takeProfit:e.takeProfit}};console.log("Order data prepared:",d);let u=await a.b.addOrder(t.uid,d);return console.log("Order added to Firestore with ID:",u),"MARKET"===e.type?(console.log("Executing market order immediately"),await this.executeOrder(u,d,i),await n.A.createTradeNotification(t.uid,"order_filled",{symbol:e.symbol,side:e.side,price:i,quantity:e.quantity})):console.log("Limit order placed, waiting for execution"),this.state.isLoading=!1,this.state.error=null,this.notifySubscribers(),console.log("Order placement successful:",u),u}catch(e){throw console.error("Order placement error:",e),this.state.isLoading=!1,this.state.error=e instanceof Error?e.message:"Failed to place order",this.notifySubscribers(),e}}async executeOrder(e,t,s){let i=o.A.getFirebaseUser();if(!i)throw console.error("Cannot execute order: User not authenticated"),Error("User not authenticated");try{console.log("Executing order:",{orderId:e,orderData:t,executionPrice:s});let r=t.origQty*s*.001;console.log("Calculated commission:",r);let l={symbol:t.symbol,side:"BUY"===t.side?"LONG":"SHORT",entryPrice:s,markPrice:s,size:t.origQty,margin:t.origQty*s/t.leverage,leverage:t.leverage,pnl:0,pnlPercent:0,liquidationPrice:this.calculateLiquidationPrice(s,"BUY"===t.side?"LONG":"SHORT",t.leverage),orderId:e,...t.stopLoss&&{stopLoss:t.stopLoss},...t.takeProfit&&{takeProfit:t.takeProfit}};console.log("Creating position:",l);let c=await a.b.addPosition(i.uid,l);console.log("Position created with ID:",c),await n.A.createTradeNotification(i.uid,"position_opened",{symbol:t.symbol,side:l.side,size:t.origQty,entryPrice:s});let d={symbol:t.symbol,side:t.side,price:s,quantity:t.origQty,commission:r,realizedPnl:0,leverage:t.leverage,orderId:e};console.log("Creating trade record:",d);let u=await a.b.addTrade(i.uid,d);if(console.log("Trade created with ID:",u),console.log("Updating order status to FILLED"),await a.b.updateOrder(e,{status:"FILLED",executedQty:t.origQty}),r>0){console.log("Updating user balance for commission");let e=o.A.getUserBalance();await o.A.updateBalance(e-r,"commission","Trading commission: ".concat(r.toFixed(2)," USDT")),console.log("Balance updated, commission deducted:",r)}console.log("Order execution completed successfully")}catch(t){console.error("Error executing order:",t);try{await a.b.updateOrder(e,{status:"FAILED"})}catch(e){console.error("Failed to update order status to FAILED:",e)}throw t}}calculateLiquidationPrice(e,t,s){let i=.995-1/s;return"LONG"===t?e*i:e/i}async cancelOrder(e){try{await a.b.updateOrder(e,{status:"CANCELLED"})}catch(e){throw console.error("Error cancelling order:",e),e}}async closePosition(e){let t=o.A.getFirebaseUser();if(t)try{var s;let i=this.state.positions.find(t=>t.id===e);if(!i)return;let r=(null===(s=this.getMarketData(i.symbol))||void 0===s?void 0:s.price)||i.markPrice,l={symbol:i.symbol,side:"LONG"===i.side?"SELL":"BUY",price:r,quantity:i.size,commission:i.size*r*.001,realizedPnl:i.pnl,leverage:i.leverage,orderId:i.orderId||"",positionId:e};await a.b.addTrade(t.uid,l);let c=o.A.getUserBalance()+i.pnl-l.commission;await o.A.updateBalance(c,i.pnl>0?"trade_profit":"trade_loss","Position closed: ".concat(i.pnl>0?"+":"").concat(i.pnl.toFixed(2)," USDT")),await a.b.deletePosition(e),await n.A.createTradeNotification(t.uid,"position_closed",{symbol:i.symbol,side:i.side,pnl:i.pnl,closePrice:r})}catch(e){throw console.error("Error closing position:",e),e}}destroy(){this.unsubscribePositions&&this.unsubscribePositions(),this.unsubscribeOrders&&this.unsubscribeOrders(),this.unsubscribeTrades&&this.unsubscribeTrades(),this.userUnsubscribe&&this.userUnsubscribe(),this.userServiceUnsubscribe&&this.userServiceUnsubscribe()}constructor(){this.state={positions:[],orders:[],trades:[],marketData:{},accountInfo:null,isLoading:!1,error:null},this.subscribers=[],this.unsubscribePositions=null,this.unsubscribeOrders=null,this.unsubscribeTrades=null,this.userUnsubscribe=null,this.userServiceUnsubscribe=null,this.orderIdCounter=1,this.positionIdCounter=1,this.tradeIdCounter=1,this.convertUserPositionToPosition=e=>{var t,s;return{id:e.id,symbol:e.symbol,side:e.side,entryPrice:e.entryPrice,markPrice:e.markPrice,size:e.size,margin:e.margin,leverage:e.leverage,pnl:e.pnl,pnlPercent:e.pnlPercent,liquidationPrice:e.liquidationPrice,stopLoss:e.stopLoss,takeProfit:e.takeProfit,timestamp:(null===(s=e.createdAt)||void 0===s?void 0:null===(t=s.toMillis)||void 0===t?void 0:t.call(s))||Date.now(),orderId:e.orderId}},this.convertUserOrderToOrder=e=>{var t,s;return{id:e.id,symbol:e.symbol,side:e.side,type:e.type,price:e.price,origQty:e.origQty,executedQty:e.executedQty,status:e.status,leverage:e.leverage,stopLoss:e.stopLoss,takeProfit:e.takeProfit,timestamp:(null===(s=e.createdAt)||void 0===s?void 0:null===(t=s.toMillis)||void 0===t?void 0:t.call(s))||Date.now()}},this.convertUserTradeToTrade=e=>{var t,s;return{id:e.id,symbol:e.symbol,side:e.side,price:e.price,quantity:e.quantity,commission:e.commission,realizedPnl:e.realizedPnl,leverage:e.leverage,timestamp:(null===(s=e.createdAt)||void 0===s?void 0:null===(t=s.toMillis)||void 0===t?void 0:t.call(s))||Date.now(),orderId:e.orderId,positionId:e.positionId}},this.initializeUserSubscription()}}let c=new l;class d{initializeSimulators(){[{symbol:"BTCUSDT",basePrice:43200,volatility:.02},{symbol:"ETHUSDT",basePrice:2320,volatility:.025},{symbol:"SOLUSDT",basePrice:142,volatility:.03},{symbol:"ADAUSDT",basePrice:.45,volatility:.035},{symbol:"XRPUSDT",basePrice:.62,volatility:.04},{symbol:"BNBUSDT",basePrice:315,volatility:.025},{symbol:"DOGEUSDT",basePrice:.08,volatility:.05},{symbol:"TRXUSDT",basePrice:.12,volatility:.04},{symbol:"LINKUSDT",basePrice:14.5,volatility:.03},{symbol:"AVAXUSDT",basePrice:38.2,volatility:.035}].forEach(e=>{this.simulators.set(e.symbol,{symbol:e.symbol,basePrice:e.basePrice,volatility:e.volatility,trend:(Math.random()-.5)*.001,lastPrice:e.basePrice,lastUpdate:Date.now()})})}start(){this.intervalId||(this.intervalId=setInterval(()=>{this.updatePrices()},1e3))}stop(){this.intervalId&&(clearInterval(this.intervalId),this.intervalId=null)}updatePrices(){this.simulators.forEach(e=>{let t=Date.now(),s=(t-e.lastUpdate)/1e3,i=(Math.random()-.5)*e.volatility*s,r=e.trend*s,a=e.lastPrice*(1+(i+r)),o=a-e.lastPrice,n=o/e.lastPrice*100;.01>Math.random()&&(e.trend=(Math.random()-.5)*.001),e.lastPrice=a,e.lastUpdate=t;let l={symbol:e.symbol,price:a,priceChange:o,priceChangePercent:n,volume:1e6*Math.random(),timestamp:t};this.notifySubscribers(e.symbol,l)})}subscribe(e){return this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);t>-1&&this.subscribers.splice(t,1)}}notifySubscribers(e,t){this.subscribers.forEach(s=>s(e,t))}getCurrentPrice(e){let t=this.simulators.get(e);return t?t.lastPrice:null}addSymbol(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:.03;this.simulators.has(e)||this.simulators.set(e,{symbol:e,basePrice:t,volatility:s,trend:(Math.random()-.5)*.001,lastPrice:t,lastUpdate:Date.now()})}removeSymbol(e){this.simulators.delete(e)}constructor(){this.simulators=new Map,this.intervalId=null,this.subscribers=[],this.initializeSimulators()}}let u=new d,b=(0,r.createContext)(void 0);function h(e){let{children:t}=e,[s,a]=(0,r.useState)({positions:[],orders:[],trades:[],marketData:{},accountInfo:null,isLoading:!1,error:null});(0,r.useEffect)(()=>c.subscribe(e=>{a(e)}),[]),(0,r.useEffect)(()=>{let e=u.subscribe((e,t)=>{c.updateMarketData(e,t)});return u.start(),()=>{e(),u.stop()}},[]);let o=(0,r.useCallback)(async e=>{try{return await c.placeOrder(e)}catch(e){throw console.error("Failed to place order:",e),e}},[]),n=(0,r.useCallback)(async e=>{try{return await c.cancelOrder(e),!0}catch(e){throw console.error("Failed to cancel order:",e),e}},[]),l=(0,r.useCallback)(async e=>{try{return await c.closePosition(e),!0}catch(e){throw console.error("Failed to close position:",e),e}},[]),d=(0,r.useCallback)(async e=>{try{return console.log("Position update not implemented in Firebase service yet:",e),!0}catch(e){throw console.error("Failed to update position:",e),e}},[]),h=(0,r.useCallback)((e,t)=>{c.updateMarketData(e,t)},[]),p=(0,r.useCallback)(e=>{console.log("Account info update not needed with Firebase service:",e)},[]),y=(0,r.useCallback)(()=>{a(e=>({...e,error:null}))},[]),m=(0,r.useCallback)(e=>c.getMarketData(e),[]),g=(0,r.useCallback)(e=>s.positions.find(t=>t.symbol===e)||null,[s.positions]),f=(0,r.useCallback)(()=>c.getAccountInfo(),[]),v=(0,r.useCallback)(()=>s.positions.reduce((e,t)=>e+t.pnl,0),[s.positions]),P=(0,r.useCallback)(()=>s.positions.reduce((e,t)=>e+t.margin,0),[s.positions]),U=(0,r.useCallback)(()=>{var e;return(null===(e=s.accountInfo)||void 0===e?void 0:e.availableBalance)||0},[s.accountInfo]),T={positions:s.positions,orders:s.orders,trades:s.trades,marketData:s.marketData,accountInfo:s.accountInfo,isLoading:s.isLoading,error:s.error,state:s,placeOrder:o,cancelOrder:n,closePosition:l,updatePosition:d,updateMarketData:h,updateAccountInfo:p,clearError:y,getMarketData:m,getPositionBySymbol:g,getAccountInfo:f,getTotalPnL:v,getTotalMargin:P,getAvailableBalance:U};return(0,i.jsx)(b.Provider,{value:T,children:t})}function p(){let e=(0,r.useContext)(b);if(void 0===e)throw Error("useTrading must be used within a TradingProvider");return e}}}]);